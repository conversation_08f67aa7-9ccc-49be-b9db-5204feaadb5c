[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/home/<USER>/circus.pid


[watcher:backend]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/backend
use_sockets=True
cmd=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/backend/.venv/bin/python server.py
stop_signal=SIGTERM
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/application.log
stdout_stream.max_bytes=104857600
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/error.log
stderr_stream.max_bytes=104857600
stderr_stream.backup_count=30
stderr_stream.time_format = %Y-%m-%d %H:%M:%S


[watcher:frontend]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/frontend/web
use_sockets=False
cmd=npm start
stop_signal=SIGTERM
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/application-web.log
stdout_stream.max_bytes=104857600
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/web-error.log
stderr_stream.max_bytes=104857600
stderr_stream.backup_count=30
stderr_stream.time_format = %Y-%m-%d %H:%M:%S
