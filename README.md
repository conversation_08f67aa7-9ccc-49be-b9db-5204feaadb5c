# 🔍 ECS 深度诊断 (ECS-Deep-Diagnose)

一个基于多智能体（Multi-Agent）架构，专为云服务器（ECS）设计的深度问题诊断与分析引擎。它融合了大型语言模型（LLM）与专业的诊断工具，旨在自动化、智能化地解决复杂的 ECS 问题。

![系统架构图](./assets/architecture.png)


## 🌟 核心特性

-   **🤖 多智能体协同架构**
    -   基于 `LangGraph` 构建，拥有协调器、规划器、研究员、编码员和报告员等多个专业角色的智能体。
    -   通过状态驱动的工作流，实现复杂任务的分解、执行与协作。

-   **🧠 灵活的语言模型 (LLM) 支持**
    -   通过 `litellm` 可轻松集成市面上绝大多数 LLM（如 OpenAI、Qwen 等）。
    -   支持分层模型调用，为不同复杂度的任务匹配最合适的模型。

-   **🛠️ 强大的工具集成**
    -   无缝集成内部 MCP 服务（如 CloudBot），利用私有领域知识进行诊断。
    -   内置网页浏览、代码执行 (Python REPL) 等多种工具，扩展信息获取与分析能力。

-   **🤝 人机协作 (Human-in-the-Loop)**
    -   支持通过自然语言交互式地审查和修改 AI 生成的诊断计划。
    -   提供类似 Notion 的块编辑器，方便对最终生成的报告进行人工润色和编辑。

-   **📝 自动化内容生成**
    -   可一键将诊断报告生成播客脚本、音频或 PowerPoint 演示文稿，满足多样化的分享需求。

## 🏗️ 系统架构

ECS 深度诊断系统采用基于 `LangGraph` 构建的模块化多智能体架构，专为自动化的研究和代码分析而设计。各个智能体组件通过定义良好的消息系统进行通信，形成一个高效、灵活的诊断工作流。

![诊断流程图](./assets/diagnose_flow.png)

系统核心组件包括：

1.  **协调器 (Coordinator)**
    -   工作流的入口，负责接收用户请求并发起诊断流程。
    -   作为用户与系统交互的主要接口，并将任务委派给规划器。

2.  **规划器 (Planner)**
    -   系统的“大脑”，负责将复杂任务分解为结构化的执行计划。
    -   评估当前信息是否充足，并决策何时需要进一步研究或生成最终报告。

3.  **研究团队 (Research Team)**
    -   执行具体任务的智能体集合，协同完成诊断计划：
    -   **研究员 (Researcher)**：利用网络搜索、网页抓取或 MCP 服务等工具收集信息。
    -   **编码员 (Coder)**：使用 Python REPL 工具执行代码、分析日志和处理技术任务。

4.  **报告员 (Reporter)**
    -   负责汇总研究团队的发现，将收集到的信息进行结构化处理，并生成全面、清晰的 Markdown 格式诊断报告。

## 🚀 快速开始

### 环境准备

请确保您的系统满足以下最低要求：

-   **Python**: `3.12+`
-   **Node.js**: `22+` (仅在需要使用 Web UI 时)

我们强烈推荐使用以下工具来简化环境管理：

-   **[`uv`](https://docs.astral.sh/uv)**: 用于极速创建 Python 虚拟环境和管理依赖。
-   **[`nvm`](https://github.com/nvm-sh/nvm)**: 用于灵活管理多个 Node.js 版本。
-   **[`pnpm`](https://pnpm.io/installation)**: 高效的 Node.js 依赖管理工具。

### 安装与配置

1.  **克隆代码仓库**
    ```bash
    git clone https://github.com/your-repo/ecs-deep-diagnose.git
    cd ecs-deep-diagnose
    ```

2.  **安装 Python 依赖**
    `uv` 会自动创建虚拟环境并安装所有必需的包。
    ```bash
    uv sync
    ```

3.  **创建配置文件**
    从模板文件复制并根据您的环境修改配置。
    ```bash
    cp .env.example .env
    cp conf.yaml.example conf.yaml
    ```
    > **重要提示**
    > 在启动前，请务必参考 **[配置指南](docs/configuration_guide.md)**，详细了解并修改 `.env` 和 `conf.yaml` 文件中的配置项（如 API Keys、模型选择等）。

4.  **(可选) 安装 Web UI 依赖**
    如果您希望使用图形化界面，请执行以下命令：
    ```bash
    cd web
    pnpm install
    ```

### 运行项目

#### 方式一：控制台模式 (推荐)

这是启动和测试项目最快捷的方式。

```bash
# 在项目根目录运行
uv run main.py
```

#### 方式二：Web 界面模式

此模式会同时启动后端 FastAPI 服务和前端 Next.js 开发服务器，提供更丰富的交互体验。

```bash
# 在项目根目录运行 (macOS / Linux)
./bootstrap.sh -d
```

启动成功后，在浏览器中打开 [`http://localhost:3000`](http://localhost:3000) 即可访问。



