# === IntelliJ IDEA / PyCharm ===
# Project-specific settings
.idea/
*.iml
*.iws
# Deprecated PyCharm/IntelliJ project files (older versions)
*.ipr

# === VS Code (Often used alongside PyCharm in teams) ===
.vscode/

# === Python Core ===
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging (common outputs)
build/
dist/
*.egg-info/
*.egg
# MANIFEST # Often generated, but can be part of source if manually maintained

# Installer logs
pip-log.txt
# pip-delete-this-directory.txt # Less common

# Unit test / coverage reports (common ones)
htmlcov/
.coverage
.coverage.*
.pytest_cache/
.tox/
# .cache # Can be too generic; ensure it's not catching wanted project data

# Jupyter Notebook Checkpoints (Notebook files themselves *.ipynb ARE usually versioned)
.ipynb_checkpoints/

# Virtual Environments (common names and files)
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
# If your venv is at the project root and named differently,
# you might ignore its components:
# pyvenv.cfg
# pip-selfcheck.json
# And platform-specific directories like:
# bin/ # Linux/macOS - CAUTION: Broad rule, can ignore wanted 'bin' dirs elsewhere
# include/
# lib/ # CAUTION: Broad rule
# lib64/ # CAUTION: Broad rule
# Scripts/ # Windows

# === OS-generated files ===
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# === Temporary and Backup Files ===
*.tmp
*.bak
*.swp
*~

# === User-Specific Ignores (from your original file) ===
# Ensure these are still relevant for your project
tests/data/api_instance_data.py
API_TOKENS.py

# === Potentially problematic from original (REVIEW IF NEEDED) ===
# The following were at the end of your original .gitignore.
# It's generally recommended to VERSION CONTROL your .ipynb files.
# If you truly want to ignore all notebooks or the 'notebooks' directory, uncomment them.
# /notebooks/
# *.ipynb

# Files and directories that should generally NOT be ignored unless for specific reasons:
# *.log # This is very broad. If you have logs you *want* to commit, remove or make specific.
# data/ # This ignores ANY directory named 'data'. Be very careful.
backend/logs
