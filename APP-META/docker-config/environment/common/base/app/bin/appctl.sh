#!/bin/bash
set -x
PROG_NAME=$0
ACTION=$1

start() {
  sh $APP_HOME/base/bin/start.sh
}

stop() {
  sh $APP_HOME/base/bin/stop.sh
}

health() {
  sh $APP_HOME/base/bin/health.sh
}

usage() {
    echo "Usage: $PROG_NAME {start|stop|restart|status}"
    exit 1;
}

case "$ACTION" in
    start)
        start
    ;;
    stop)
        stop
    ;;
    restart)
        stop
        start
    ;;
    status)
        health
    ;;
    *)
        usage
    ;;
esac
