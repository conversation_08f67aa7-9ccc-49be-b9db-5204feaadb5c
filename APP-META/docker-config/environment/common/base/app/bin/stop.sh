#!/bin/bash
set -x

echo "stop at: $(date +"%Y-%m-%d %H:%M:%S")" >> $APP_HOME/logs/deploy.log
source ${APP_HOME}/base/bin/setenv.sh

NGINX_SKIP=${NGINX_SKIP:-"0"}
if [ "${NGINX_SKIP}" -ne "1" ]; then
    echo "make /status.taobao is 404" >> $APP_HOME/logs/deploy.log
    rm -rf /home/<USER>/app.online
    echo "waiting sync vip server..." >> $APP_HOME/logs/deploy.log
    for i in $(seq 1 $MAX_STOP_VIPSERVER_TIMEOUT_SECONDS); do
      echo "...${i}s" >> $APP_HOME/logs/deploy.log
      sleep 1
    done
    echo "stop nginx by sh $NGINXCTL stop" >> $APP_HOME/logs/deploy.log
    $NGINXCTL stop
fi

echo "stop apps by ${APP_HOME}/custom/stop.sh" >> $APP_HOME/logs/deploy.log
stop_app

echo "waiting to stop..." >> $APP_HOME/logs/deploy.log
for i in $(seq 1 $MAX_STOP_TIMEOUT_SECONDS); do
  echo "...${i}s" >> $APP_HOME/logs/deploy.log
  sleep 1
done

echo "force stop app"  >> $APP_HOME/logs/deploy.log
force_stop_app

if [ "0" -ne "$?" ]; then
  exit 1
else
  exit 0
fi
