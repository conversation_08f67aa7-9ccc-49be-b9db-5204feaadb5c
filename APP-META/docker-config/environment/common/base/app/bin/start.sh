#!/bin/bash
set -x

echo "=============================================="
echo "ready to start at: $(date +"%Y-%m-%d %H:%M:%S")"
die() {
    if [ "$#" -gt 0 ]; then
        echo "ERROR:" "$@"
    fi
    exit 128
}

extract_tgz() {
    local tgz_path="$1"
    local dir_path="$2"

    echo "extract ${tgz_path}"
    cd "${APP_HOME}/target" || exit 1
    rm -rf "${dir_path}" || exit 1
    tar xzf "${tgz_path}" || exit 1
    test -d "${dir_path}" || die "ERROR: no directory: ${dir_path}"
    touch --reference "${tgz_path}" "${tgz_path}.timestamp" || exit 1
}

update_target() {
    local tgz_name="$1"
    local dir_name="$2"

    local tgz_path="${APP_HOME}/target/${tgz_name}"
    local dir_path="${APP_HOME}/target/${dir_name}"

    local error=0
    # dir exists
    if [ -d "${dir_path}" ]; then
        # tgz exists
        if [ -f "${tgz_path}" ]; then
            local need_tar=0
            if [ ! -e "${tgz_path}.timestamp" ]; then
                need_tar=1
            else
                local tgz_time=$(stat -L -c "%Y" "${tgz_path}")
                local last_time=$(stat -L -c "%Y" "${tgz_path}.timestamp")
                if [ $tgz_time -gt $last_time ]; then
                    need_tar=1
                fi
            fi
            # tgz is new - extract_tgz
            if [ "${need_tar}" -eq 1 ]; then
                extract_tgz "${tgz_path}" "${dir_path}"
            fi
            # tgz is not new - return SUCCESS
        fi
        # tgz not exists - return SUCCESS
    # dir not exists
    else
        # tgz exists - extract_tgz
        if [ -f "${tgz_path}" ]; then
            extract_tgz "${tgz_path}" "${dir_path}"
        fi
    fi

    return $error
}


if [ "$UID" -eq 0 ]; then
    echo "can't run as root, please use: sudo -u admin $0 $@"
    exit 1
fi

source "$APP_HOME/base/bin/setenv.sh"

mkdir -p "${APP_HOME}/target" || exit
mkdir -p "${APP_HOME}/logs" || exit
mkdir -p "${STATUSROOT_HOME}" || exit

echo "extract_tgz ${APP_NAME}.tgz to ${APP_HOME}/target/${APP_NAME}"
#update_target "${APP_NAME}.tgz" "${APP_NAME}" || exit 1


echo "run ~/custom/bin/start.sh start_app"

start_app

if [ "0" = "$?" ]; then
  echo "start running"
else
  echo "start failed"
  exit 1
fi

result=1
# 每秒，判断${APP_HOME}/bin/health.sh是否返回0，若返回0，继续，如果超过MAX_START_TIMEOUT_SECONDS秒，返回1
for i in $(seq 1 $MAX_START_TIMEOUT_SECONDS); do
  ${APP_HOME}/base/bin/health.sh
  if [ "0" = "$?" ]; then
    echo "start finished"
    result=0
    break
  fi
  echo "...${i}s"
  sleep 1
done

if [ "${NGINX_SKIP}" -ne "1" ]; then
    echo "start nginx"
    $NGINXCTL start
    echo "nginx started"
fi

# 按照yyyy-mm-dd HH:mm:ss格式输出当前时间
now=$(date +"%Y-%m-%d %H:%M:%S")
echo "app started at: $now"

exit $result
