#!/bin/bash
set -x


health_check() {
  if [ -f "/home/<USER>/app.online" ]; then
    result=$(cat /home/<USER>/app.online)
    if [ "success" = "$result" ]; then
      echo "health check[YES] app.online = $result"
      return 0
    else
      echo "health check[NO] app.online = $result"
      return 1
    fi
  else
    # 判断http://localhost:80/status.taobao是否成功code=200并返回success，如果成功，调用python3 lifecycle.py中的online函数
    server_status=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:8000/status.taobao)
    server_result=$(curl http://127.0.0.1:8000/status.taobao)
    if [ $server_status -eq 200 ] && [ $server_result == "success" ]; then
      echo "health check[YES] /status.taobao $server_status $server_result"
    else
      echo "health check[NO] /status.taobao $server_status $server_result"
    fi
    return 1
  fi
}
