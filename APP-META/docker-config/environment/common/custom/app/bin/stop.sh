#!/bin/bash
set -x

# 停止应用
stop_app() {
  if { test -r "${CIRCUS_PID}" && kill -0 "$(cat "${CIRCUS_PID}")"; }; then
      circusctl stop || exit 1
  fi
  return 0
}

# 强制停止应用
force_stop_app() {
  health_file="/home/<USER>/app.online"
  # 文件存在删掉，等待15秒
  if [ -f "${health_file}" ]; then
    echo "force remove ${health_file}"  >> $APP_HOME/logs/deploy.log
    rm -f "${health_file}"
    sleep ${MAX_STOP_VIPSERVER_TIMEOUT_SECONDS}
  fi

  # 如果应用还在运行，强制停止
  if { test -r "${CIRCUS_PID}" && kill -0 "$(cat "${CIRCUS_PID}")"; }; then
      echo "force kill -9 $(cat "${CIRCUS_PID}")"  >> $APP_HOME/logs/deploy.log
      kill -9 $(cat "${CIRCUS_PID}")
  fi

  return 0
}
