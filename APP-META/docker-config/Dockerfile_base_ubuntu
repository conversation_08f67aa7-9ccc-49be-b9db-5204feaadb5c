# 基础镜像 (假定为基于 Ubuntu 22.04，并预装了 Python 3.10)
FROM hub.docker.alibaba-inc.com/aone-base-global/ubuntu-python-310:1.4

# --- 构建参数 ---
# ... (您的 ARG 声明保持不变) ...
ARG PYTHON_SOURCE_FILENAME=Python-3.12.0.tgz
ARG PYTHON_VERSION_DIR=Python-3.12.0
ARG PYTHON_INSTALL_PREFIX=/opt/python-3.12.0

ARG NODE_SOURCE_FILENAME=node-v24.1.0.tar.gz
ARG NODE_VERSION_DIR=node-v24.1.0
ARG NODE_INSTALL_PATH=/usr/local/lib/nodejs

ARG NODE_BINARY_FILENAME=node-v23.9.0-linux-x64.tar.gz
ARG NODE_EXTRACTED_DIR_NAME=node-v23.9.0-linux-x64
ARG NODE_INSTALL_PREFIX=/usr/local/nodejs

ARG UV_ARCHIVE_FILENAME=uv-x86_64-unknown-linux-musl.tar.gz
ARG UV_EXTRACTED_DIR_NAME=uv-x86_64-unknown-linux-musl

ENV APP_NAME ecs-deep-diagnose
ENV APP_HOME /home/<USER>/$APP_NAME
ENV ADMIN_HOME /home/<USER>

USER admin
# --- 安装编译依赖和所需软件 ---
RUN sudo apt-get update && \
    sudo apt-get install -y --no-install-recommends \
        build-essential \
        zlib1g-dev \
        libncurses5-dev \
        libgdbm-dev \
        libnss3-dev \
        libssl-dev \
        libsqlite3-dev \
        libreadline-dev \
        libffi-dev \
        curl \
        libbz2-dev \
        pkg-config \
        make \
        wget \
        xz-utils \
        tk-dev \
        libbluetooth-dev \
        liblzma-dev \
        nginx \
        logrotate \
        lsb-release \
        software-properties-common \
        # 确保 GPG 相关工具已安装，用于方案 B
        gnupg \
        ca-certificates && \
    sudo apt-get clean && \
    sudo rm -rf /var/lib/apt/lists/*

# 步骤 1.5: 检查 Ubuntu 版本和 gcc-12 在默认仓库中的可用性
RUN echo ">>>> CHECKING UBUNTU VERSION AND GCC-12 AVAILABILITY <<<<" && \
    UBUNTU_CODENAME=$(lsb_release -sc) && \
    echo "Ubuntu Codename: ${UBUNTU_CODENAME}" && \
    echo "Attempting to show gcc-12 policy from default repositories:" && \
    apt-cache policy gcc-12 && \
    echo ">>>> END OF CHECK <<<<"

# --- GCC 安装 ---
# 【方案 A: 如果 gcc-12 在默认仓库中可用 (例如 Ubuntu 22.04 Jammy 或更新版本) - 默认尝试此方案】
# 如果上面的检查显示 gcc-12 可从主仓库安装 (Candidate 不是 (none) 或类似)，此方案应该可行。
RUN echo "Attempting to install gcc-12 and g++-12 directly (Scheme A)..." && \
    sudo apt-get update && \
    sudo apt-get install -y gcc-12 g++-12 && \
    sudo apt-get clean && \
    sudo rm -rf /var/lib/apt/lists/* && \
    echo "Scheme A: gcc-12/g++-12 installation successful." || \
    (echo "Scheme A failed. If your Ubuntu version is older (e.g., focal) and gcc-12 is not in default repos, you might need Scheme B." && false)

# 【方案 B: 如果方案 A 失败，并且您确认需要 PPA (例如，基础镜像是较旧的 Ubuntu 版本，如 20.04 Focal)】
# # 要启用方案 B:
# # 1. 注释掉上面方案 A 中的整个 `RUN echo "Attempting to install gcc-12..." ... false)` 块。
# # 2. 取消注释下面的所有行，从 `# RUN echo "Attempting to install gcc-12 via PPA (Scheme B)..."` 开始。

# # RUN echo "Attempting to install gcc-12 via PPA (Scheme B)..." && \
# #     apt-get update && \
# #     # 手动添加 PPA 密钥和源
# #     # PPA: ubuntu-toolchain-r/test
# #     # GPG Key ID: 60C317803A41BA51845E371A1E9377A2BA9EF27F
# #     curl -fsSL "https://keyserver.ubuntu.com/pks/lookup?op=get&search=0x60C317803A41BA51845E371A1E9377A2BA9EF27F" | gpg --dearmor -o /usr/share/keyrings/ubuntu-toolchain-r-test-keyring.gpg && \
# #     echo "deb [signed-by=/usr/share/keyrings/ubuntu-toolchain-r-test-keyring.gpg] http://ppa.launchpad.net/ubuntu-toolchain-r/test/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ubuntu-toolchain-r-test.list && \
# #     apt-get clean && \
# #     rm -rf /var/lib/apt/lists/*
# #
# # # 更新包列表以包含新 PPA
# # RUN apt-get update
# #
# # # 从 PPA 安装 GCC 12 和 G++ 12
# # RUN apt-get install -y gcc-12 g++-12 && \
# #     apt-get clean && \
# #     rm -rf /var/lib/apt/lists/* && \
# #     echo "Scheme B: gcc-12/g++-12 installation via PPA successful."


# --- 安装 Python 3.12.0 从源码 ---
WORKDIR /home/<USER>
COPY files/${PYTHON_SOURCE_FILENAME} .
RUN sudo mkdir -p ${PYTHON_INSTALL_PREFIX}
RUN sudo chmod +777  ${PYTHON_INSTALL_PREFIX}
RUN tar xzf ${PYTHON_SOURCE_FILENAME}
RUN cd ${PYTHON_VERSION_DIR} && ./configure --prefix=${PYTHON_INSTALL_PREFIX} --enable-optimizations --with-ensurepip=install --with-openssl=/usr --with-openssl-rpath=auto && make -j$(nproc) && make install

# 确保在编译 Node.js 之前设置 CC 和 CXX (如果 Node.js 源码编译需要特定 GCC)
# 如果您只是使用预编译的 Node.js，这一步可能不是必需的，但如果源码编译 Node.js，则很重要
ENV CC=/usr/bin/gcc-12
ENV CXX=/usr/bin/g++-12


# --- 安装 Node.js (使用预编译二进制包) ---
WORKDIR /home/<USER>
COPY files/${NODE_BINARY_FILENAME} .
# 使用 sudo 进行需要 root 权限的操作
RUN sudo mkdir -p ${NODE_INSTALL_PREFIX}
RUN    sudo chmod +777 ${NODE_INSTALL_PREFIX}
RUN    sudo tar -xzf ${NODE_BINARY_FILENAME} -C ${NODE_INSTALL_PREFIX} --strip-components=1

RUN sudo ln -sf ${NODE_INSTALL_PREFIX}/bin/node /usr/local/bin/node && \
    sudo ln -sf ${NODE_INSTALL_PREFIX}/bin/npm /usr/local/bin/npm && \
    sudo ln -sf ${NODE_INSTALL_PREFIX}/bin/npx /usr/local/bin/npx

# --- 安装 uv ---
WORKDIR /home/<USER>
COPY files/${UV_ARCHIVE_FILENAME} .
# 警告：您脚本中的 UV_ARCHIVE_FILENAME 指向 musl 版本。
# 对于 Ubuntu (glibc)，您应该使用 '...-linux-gnu.tar.gz' 版本。
# 请确认 files/${UV_ARCHIVE_FILENAME} 是正确的版本。
RUN sudo tar -xzf ${UV_ARCHIVE_FILENAME} && \
    sudo mv "./${UV_EXTRACTED_DIR_NAME}/uv" /usr/local/bin/uv && \
    sudo chmod +x /usr/local/bin/uv

# --- 设置环境变量 PATH ---
ENV PATH=${PYTHON_INSTALL_PREFIX}/bin:/usr/local/bin:${PATH}

WORKDIR /home/<USER>
RUN sudo apt-get update && \
    # 确保 software-properties-common 安装，用于 add-apt-repository
    # 添加 universe 仓库 (如果尚未启用), libpcre3-dev 和 libluajit-5.1-dev 通常在这里
    sudo add-apt-repository -y universe && \
    # 再次更新包列表以包含新仓库
    sudo apt-get update && \
    sudo apt-get install -y --no-install-recommends \
        # software-properties-common, gnupg, ca-certificates 已在上面安装
        # --- 添加 Tengine 依赖 ---
        libpcre3-dev \
        libluajit-5.1-dev

# --- Tengine 安装 ---
ARG TENGINE_VERSION=2.4.1
ARG TENGINE_TARBALL=tengine-${TENGINE_VERSION}.tar.gz
# Tengine 编译通常需要 root 权限或 sudo
# 并且其依赖（如 pcre-devel, openssl-devel 等）应在前面的 apt-get install 中确保
# 此处暂时以 admin 用户运行，但编译和安装通常需要更高权限或配置 sudo
# RUN sudo apt-get update && sudo apt-get install -y libpcre3-dev libssl-dev zlib1g-dev # 确保 Tengine 依赖
RUN wget https://tengine.taobao.org/download/${TENGINE_TARBALL}
RUN    tar -zxvf ${TENGINE_TARBALL}
RUN    cd tengine-${TENGINE_VERSION} && ./configure --prefix=/opt/taobao/tengine  --with-http_ssl_module  && \
    make -j$(nproc) && \
    sudo make install

# --- 用户特定配置 ---
RUN sudo ln -sf ${PYTHON_INSTALL_PREFIX}/bin/python3.12 /usr/bin/python3 && \
    sudo ln -sf ${PYTHON_INSTALL_PREFIX}/bin/pip3 /usr/bin/pip3 && \
    sudo ln -sf ${PYTHON_INSTALL_PREFIX}/bin/pip3 /usr/bin/pip && \
    sudo ln -sf ${PYTHON_INSTALL_PREFIX}/bin/python3.12 /usr/bin/python

# 更新 PATH 以包含 Node.js (如果 NODE_INSTALL_PATH 与之前不同)
# 您之前已经将 /usr/local/bin 添加到 PATH，并且 Node.js 被链接到那里，所以可能不需要再次修改
# ENV PATH=${PYTHON_INSTALL_PREFIX}/bin:${NODE_INSTALL_PATH}/bin:/usr/local/bin:${PATH} # 检查是否重复或需要调整

RUN npm -v
RUN sudo npm install -g pnpm

WORKDIR /home/<USER>

RUN python3 -m pip install --upgrade pip
RUN npm -v
RUN uv --version
RUN sudo npm install -g @antv/mcp-server-chart
RUN /usr/bin/pip install circus
