# CentOS-SCLo-sclo.repo
#
# Please see http://wiki.centos.org/SpecialInterestGroup/SCLo for more
# information
# Modified to use Aliyun mirrors for CentOS 7 EOL

[centos-sclo-sclo]
name=CentOS-7 - SCLo sclo - Aliyun
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/sclo/
#mirrorlist=http://vault.centos.org?arch=$basearch&release=7&repo=sclo-sclo
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-sclo-testing]
name=CentOS-7 - SCLo sclo Testing - Aliyun
# Testing repos are usually not available or needed post-EOL. Pointing to a valid base path.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/sclo/
gpgcheck=0
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-sclo-source]
name=CentOS-7 - SCLo sclo Sources - Aliyun
# Aliyun typically mirrors source packages as well.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/Source/sclo/
# If Aliyun path is not found, fallback to vault:
# baseurl=http://vault.centos.org/7.9.2009/sclo/Source/sclo/
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-sclo-debuginfo]
name=CentOS-7 - SCLo sclo Debuginfo - Aliyun
# Debuginfo paths can be tricky. Pointing to a base path or vault.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/sclo/
# Alternative if Aliyun has a specific debug path or fallback to vault:
# baseurl=http://mirrors.aliyun.com/centos/7/debug/sclo/$basearch/sclo/
# baseurl=http://vault.centos.org/7.9.2009/sclo/$basearch/sclodebug/ (Vault path for debuginfo might differ)
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo
