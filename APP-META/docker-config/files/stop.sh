#!/bin/bash
set -x
# --- 1. 定义日志文件路径 (参考 start.sh) ---
APP_BASE_DIR="/home/<USER>/ecs-deep-diagnose"
LOG_DIR="${APP_BASE_DIR}/logs"
LOG_FILE="${LOG_DIR}/stop.log"

# --- 2. 确保日志目录和文件存在，避免重定向失败 ---
# 使用 sudo 确保有权限创建
sudo mkdir -p "$LOG_DIR"
sudo touch "$LOG_FILE"
# 确保 admin 用户也有权限写入，因为有 sudo -u admin 命令
sudo chown admin:admin "$LOG_FILE"


# --- 以下为原始脚本逻辑，仅增加了输出重定向 ---

source /kubeone/lib/sys_info.sh


APPNAME=${APPNAME:-$SIGMA_APP_NAME}
APPNAME=${APPNAME:-$APP_NAME}

# 如果 APPNAME 不存在，将错误信息也记录到日志中
if [ x"$APPNAME" == x ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') | [ERROR] APPNAME not exist" >> "$LOG_FILE"
    error_exit "APPNAME not exist"
fi

# 将所有命令的输出(标准输出和标准错误)都追加(>>)到日志文件中
echo "$(date '+%Y-%m-%d %H:%M:%S') | $APPNAME begin stop..." >> "$LOG_FILE" 2>&1
sudo -u admin -H -E /home/<USER>/$APPNAME/bin/appctl.sh stop >> "$LOG_FILE" 2>&1

# 避免机器置换日志未采集完
# restart改为stop,避免checkpoint不一致
echo "$(date '+%Y-%m-%d %H:%M:%S') | Stopping ilogtaild..." >> "$LOG_FILE" 2>&1
sudo /etc/init.d/ilogtaild stop >> "$LOG_FILE" 2>&1

