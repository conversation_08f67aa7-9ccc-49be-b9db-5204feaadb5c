# CentOS-SCLo-rh.repo
#
# Please see http://wiki.centos.org/SpecialInterestGroup/SCLo for more
# information
# Modified to use Aliyun mirrors for CentOS 7 EOL

[centos-sclo-rh]
name=CentOS-7 - SCLo rh - Aliyun
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/rh/
#mirrorlist=http://vault.centos.org?arch=$basearch&release=7&repo=sclo-rh
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-rh-testing]
name=CentOS-7 - SCLo rh Testing - Aliyun
# Testing repos are usually not available or needed post-EOL. Pointing to a valid base path.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/rh/
gpgcheck=0
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-rh-source]
name=CentOS-7 - SCLo rh Sources - <PERSON>yun
# Aliyun typically mirrors source packages as well.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/Source/rh/
# If Aliyun path is not found, fallback to vault:
# baseurl=http://vault.centos.org/7.9.2009/sclo/Source/rh/
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo

[centos-sclo-rh-debuginfo]
name=CentOS-7 - SCLo rh Debuginfo - Aliyun
# Debuginfo paths can be tricky. Pointing to a base path or vault.
baseurl=http://mirrors.aliyun.com/centos/7/sclo/$basearch/rh/
# Alternative if Aliyun has a specific debug path or fallback to vault:
# baseurl=http://mirrors.aliyun.com/centos/7/debug/sclo/$basearch/rh/
# baseurl=http://vault.centos.org/7.9.2009/sclo/$basearch/rhdebug/ (Vault path for debuginfo might differ)
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo
