#!/bin/bash
set -x
# --- 定义变量，方便维护 ---
APP_BASE_DIR="/home/<USER>/ecs-deep-diagnose"
LOG_DIR="${APP_BASE_DIR}/logs"
LOG_FILE="${LOG_DIR}/deploy.log"

echo ""
echo -e "==> START RUN $0 kube_version: ${KUBEONE_VERSION} user: $(whoami)" | ts

# --- 修复点 1: 检查环境变量文件是否存在 ---
# 检查 KUBEONE_ENV_FILE 变量是否设置，并且对应的文件是否存在
if [ -z "$KUBEONE_ENV_FILE" ]; then
    echo "[WARN] KUBEONE_ENV_FILE is not set. Skipping source."
elif [ ! -f "$KUBEONE_ENV_FILE" ]; then
    # 如果文件不存在，则打印错误并退出，而不是让脚本崩溃
    echo "[ERROR] Environment file not found at: $KUBEONE_ENV_FILE" >&2
    #exit 1
else
    source "$KUBEONE_ENV_FILE" > /dev/null
fi

# 引入函数库 (假设此文件总是存在)
source /kubeone/lib/sys_info.sh

# 检查当前用户是否为 admin
not_user_exit admin

# 获取应用名称
APPNAME=${APPNAME:-$SIGMA_APP_NAME}
APPNAME=${APPNAME:-$APP_NAME}
[ -z "$APPNAME" ] && error_exit "APPNAME not exist"

# --- 修复点 2: 创建目录和日志文件，并设置权限 ---
# 确保 APP_BASE_DIR 目录存在并有基本权限
echo "==> Ensuring base directory exists: ${APP_BASE_DIR}"
sudo mkdir -p "$APP_BASE_DIR"
sudo chmod 755 "$APP_BASE_DIR" # 777 过于危险，755 通常足够
sudo chown -R admin:admin "$APP_BASE_DIR" # 将所有权交给 admin

# 确保日志目录存在
echo "==> Ensuring log directory exists: ${LOG_DIR}"
mkdir -p "$LOG_DIR" # admin 用户现在有权限创建子目录，无需 sudo

# 确保日志文件存在，然后再更改其权限
echo "==> Ensuring log file exists: ${LOG_FILE}"
touch "$LOG_FILE" # admin 用户创建该文件

# --- 修复点 3: 启动应用，现在重定向不会再失败 ---
echo "==> Starting application: ${APPNAME}"
# 现在 admin 用户对日志文件有所有权和写权限，重定向可以正常工作
/home/<USER>/ecs-deep-diagnose/base/bin/appctl.sh start >> "$LOG_FILE" 2>&1

echo "==> FINISH RUN $0" | ts
