# 用基础镜像地址替换下方镜像地址
FROM hub.docker.alibaba-inc.com/aone-base/ecs-deep-diagnose_nodejs:20250604105446

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ENV APP_NAME ecs-deep-diagnose
ENV APP_ENV daily
ENV APP_HOME /home/<USER>/$APP_NAME
ENV ADMIN_HOME /home/<USER>
RUN mkdir -p $APP_HOME/base && \
    mkdir -p $APP_HOME/custom && \
    mkdir -p $APP_HOME/bin && \
    mkdir -p $APP_HOME/conf && \
    mkdir -p $APP_HOME/target && \
    mkdir -p $ADMIN_HOME/logs && \
    echo "$APP_HOME/base/bin/appctl.sh status" >> $ADMIN_HOME/health.sh

COPY files/start.sh   $ADMIN_HOME/start.sh
RUN sudo chmod +x $ADMIN_HOME/start.sh

COPY files/stop.sh   $ADMIN_HOME/stop.sh
RUN sudo chmod +x $ADMIN_HOME/stop.sh


COPY $APP_NAME.tgz $APP_HOME/target/
COPY environment/common/base/cai $ADMIN_HOME/cai/
COPY environment/common/base/app $APP_HOME/base/
COPY environment/common/custom/app $APP_HOME/custom/

# 设置文件操作权限
RUN sudo chmod -R a+x $APP_HOME/base/bin/ && \
    sudo chmod -R a+x $APP_HOME/custom/bin/ && \
    sudo chmod +x $ADMIN_HOME/*.sh && \
     ln -s $APP_HOME/base/bin/appctl.sh $APP_HOME/bin/appctl.sh && \
     ln -s $APP_HOME/base/bin/start.sh $APP_HOME/bin/start.sh && \
     ln -s $APP_HOME/base/bin/stop.sh $APP_HOME/bin/stop.sh && \
     ln -s $APP_HOME/base/bin/health.sh $APP_HOME/bin/health.sh && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz -C $APP_HOME/target/ && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz -C $ADMIN_HOME $APP_NAME/conf && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz $APP_NAME/antx.properties -O > $APP_HOME/target/antx.properties

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME $ADMIN_HOME/logs \
       $ADMIN_HOME/cai/logs \
       $APP_HOME/logs \
       $APP_HOME/data

# 启动容器时进入的工作目录
WORKDIR $APP_HOME/



RUN cp $APP_HOME/target/$APP_NAME/APP-META/docker-config/environment/daily/.env $APP_HOME/target/$APP_NAME/backend
RUN cp $APP_HOME/target/$APP_NAME/APP-META/docker-config/environment/daily/.env $APP_HOME/target/$APP_NAME/frontend/web

RUN npm config set registry https://registry.npmmirror.com
RUN cd $APP_HOME/target/$APP_NAME/frontend/web && npm install   && npm run build

RUN  /usr/bin/pip install circus -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com
RUN cd $APP_HOME/target/$APP_NAME/backend && uv sync  --index-url https://mirrors.aliyun.com/pypi/simple/

RUN rm -rf /home/<USER>/Python-3.12.0 \
    /home/<USER>/openssl-1.1.1q \
    /home/<USER>/uv-x86_64-unknown-linux-musl \
    && rm -f /home/<USER>/openssl-1.1.1q.tar.gz \
    /home/<USER>/uv-x86_64-unknown-linux-musl.tar.gz \
    && chown admin:admin /home/<USER>/ecs-deep-diagnose
