--extra-index-url http://yum.tbsite.net/aliyun-pypi/simple/
--trusted-host yum.tbsite.net
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
alibabacloud-darabonba-string==0.0.4
alibabacloud-tea==0.4.3
alibabacloud_credentials==0.3.6
alibabacloud_darabonba_encode_util==0.0.2
alibabacloud_darabonba_map==0.0.1
alibabacloud_gateway_spi==0.0.3
alibabacloud_ha3engine==1.3.12
alibabacloud_openapi_util==0.2.2
alibabacloud_tea_openapi==0.3.12
alibabacloud_tea_util==0.3.13
alibabacloud_tea_xml==0.0.2
aliyun-log-python-sdk==0.9.19
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
asttokens==3.0.0
attrs==25.3.0
backcall==0.2.0
beautifulsoup4==4.13.4
bleach==6.2.0
blinker==1.8.2
certifi==2023.11.17
cffi==1.17.1
charset-normalizer==3.4.0
circus==0.18.0
click==8.1.7
cryptography==45.0.2
dashscope==1.23.2
dateparser==1.2.1
decorator==5.2.1
defusedxml==0.7.1
distro==1.9.0
dnspython==2.7.0
docopt==0.6.2
dotenv==0.9.9
edm-python-sdk==0.3.4
elastic-transport==8.17.1
elasticsearch==9.0.1
email_validator==2.2.0
eval_type_backport==0.2.2
exceptiongroup==1.2.2
executing==2.2.0
fastapi==0.115.12
fastapi-cli==0.0.7
fastjsonschema==2.21.1
fastmcp==2.3.3
Flask==2.3.3
frozenlist==1.6.0
gunicorn==22.0.0
h11==0.14.0
httpcore==1.0.6
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
iniconfig==2.0.0
ipython==8.12.3
itsdangerous==2.2.0
jedi==0.19.2
jieba==0.42.1
Jinja2==3.1.4
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
json5==0.12.0
jsonlines==4.0.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
langchain==0.3.24
langchain-core==0.3.56
langchain-mcp-adapters==0.0.11
langchain-openai==0.3.14
langchain-text-splitters==0.3.8
langsmith==0.3.42
lz4==4.4.4
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib-inline==0.1.7
mcp==1.8.1
mdurl==0.1.2
mistune==3.1.3
multidict==6.4.3
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
numpy==1.26.4
openai==1.78.0
openapi-pydantic==0.5.1
orjson==3.10.16
packaging==23.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pickleshare==0.7.5
pipreqs==0.5.0
platformdirs==4.3.7
pluggy==1.5.0
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.4
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser==2.22
pycryptodome==3.20.0
pydantic==2.9.2
pydantic-settings==2.6.1
pydantic_core==2.23.4
Pygments==2.18.0
pytest==8.3.2
pytest-asyncio==0.25.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.0.3
qwen-agent==0.0.22
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
rich-toolkit==0.14.3
rpds-py==0.24.0
scikit-learn==1.4.1.post1
scipy==1.11.0
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
sort-requirements==1.3.0
soupsieve==2.7
SQLAlchemy==2.0.40
sse-starlette==2.1.2
stack-data==0.6.3
starlette==0.46.2
tabulate==0.9.0
tenacity==9.1.2
threadpoolctl==3.5.0
tiktoken==0.9.0
tinycss2==1.4.0
tornado==6.4.1
tqdm==4.67.1
traitlets==5.14.3
typer==0.15.2
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.3.1
ujson==5.10.0
urllib3==2.2.3
uvicorn==0.30.1
uvloop==0.21.0
watchfiles==1.0.5
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
yarg==0.1.9
yarl==1.20.0
zstandard==0.23.0
