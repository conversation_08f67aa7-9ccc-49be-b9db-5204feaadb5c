FROM reg.docker.alibaba-inc.com/amwp/kubeone-base:latest

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ENV APP_NAME ecs-deep-diagnose

ARG NODE_PACKAGE_FILENAME=node-v24.1.0.tar.gz
ARG UV_ARCHIVE_FILENAME=uv-x86_64-unknown-linux-musl.tar.gz

# 步骤 1: 安装基础编译工具和开发库 (这些通常不常变动)
RUN yum install -y gcc gcc-c++ zlib-devel bzip2-devel bison pcre-devel openssl-devel libffi-devel  sqlite-devel && \
    yum clean all

# 步骤 2: 安装来自特定 URL 的 RPM 包 (这些是自定义或内部包)
RUN { rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/tengine-proxy/tengine-proxy-2.1.13-20170802132414.el7u2.x86_64.rpm" || true; } && \
    rpm -ivh --nodeps "http://yum.tbsite.net/alios/7/os/x86_64/Packages/taobao-cronolog-1.6.2-15.alios7.x86_64.rpm" && \
    rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/c_tbip/c_tbip-2.0.7-1340125.4d1bec0.el7.x86_64.rpm"
    # 这里不执行 yum clean all，因为它们不是通过 yum 安装的，且 rpm 安装后通常不需要立即清理 yum 缓存

# install for gcc upgrade
RUN sudo yum makecache
RUN  sudo yum install -y glibc-devel
RUN sudo yum update -y openssh util-linux bash-completion gnupg2 python perl
RUN sudo yum install -y alios7u-2_32-gcc-10-repo
RUN sudo yum update -y libdb
RUN sudo yum install gcc binutils -y
RUN sudo yum install -y glibc glibc-langpack-en

# 步骤 4: 执行 staragent 的安装后脚本并重启服务
# 这些操作依赖于 t-staragent-agent2 的成功安装
RUN bash /home/<USER>/bin/post_install.sh && \
    sudo /etc/init.d/staragentctl restart
    # 或者，如果 post_install.sh 内部会重启服务，则可能不需要显式调用 restart


# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN    yum install -yb current t-staragent-agent2  && \
    yum -y install pcre  && \
#   yum -y install  openssl openssl-devel -y && \
    yum install -y m17n-db-chinese  && \
    yum install -y mysql  && \
    yum install libffi-devel -y && \
    yum install xz-devel -y && \
    yum clean all \
    && bash /home/<USER>/bin/post_install.sh  \
    && sudo /etc/init.d/staragentctl restart

COPY files/openssl-1.1.1q.tar.gz .
RUN tar -zxvf openssl-1.1.1q.tar.gz && \
    cd openssl-1.1.1q && \
    ./config --prefix=/usr/local/openssl && \
    make -j4 && \
    sudo make install
RUN sudo ln -sf /usr/local/bin/openssl /usr/bin/openssl
RUN sudo ln -sf /usr/local/include/openssl /usr/include/openssl
RUN sudo ln -sf /usr/local/lib64/libssl.so.1.1 /usr/lib64/libssl.so.1.1
RUN sudo ln -sf /usr/local/lib64/libcrypto.so.1.1 /usr/lib64/libcrypto.so.1.1
RUN sudo echo "/usr/local/lib/" >> /etc/ld.so.conf
ENV LD_LIBRARY_PATH /usr/local/openssl/lib:$LD_LIBRARY_PATH


WORKDIR /home/<USER>

RUN yum -y update && \
    yum -y install wget unzip

# 安装 OpenJDK 8
RUN yum -y install java-1.8.0-openjdk-devel



ADD files/epel.repo /etc/yum.repos.d
ADD files/CentOS-SCLo-scl.repo /etc/yum.repos.d
ADD files/CentOS-SCLo-scl-rh.repo /etc/yum.repos.d
ADD files/Centos-7.repo /etc/yum.repos.d
ADD files/epel-testing.repo /etc/yum.repos.d

RUN yum install -y centos-release-scl \
&& yum install -y devtoolset-11 \
&& scl enable devtoolset-11 bash \
&& echo "source /opt/rh/devtoolset-11/enable" >> ~/.bashrc \
&& source ~/.bashrc \
&& source /opt/rh/devtoolset-11/enable \
&& gcc --version
RUN yum install -y git curl wget


# 设置 JAVA_HOME 环境变量
ENV JAVA_HOME /usr/lib/jvm/java-1.8.0-openjdk
ENV PATH $JAVA_HOME/bin:$PATH:/home/<USER>/bin/

COPY files/Python-3.12.0.tgz .
RUN tar xzf Python-3.12.0.tgz && \
    cd Python-3.12.0 && \
    export LDFLAGS="-L/usr/local/openssl/lib" && \
    export CPPFLAGS="-I/usr/local/openssl/include" && \
    ./configure --prefix=/home/<USER>/usr/local/openssl  && \
    make -j4 && \
    make install

RUN ln -sf /home/<USER>/bin/python3.12 /usr/bin/python3 \
    && ln -sf /home/<USER>/bin/pip3.12 /usr/bin/pip3 \
    && ln -sf /home/<USER>/bin/pip3.12 /usr/bin/pip \
    && ln -sf /home/<USER>/bin/python3.12 /usr/bin/python

RUN /usr/bin/pip install --upgrade pip  -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com


COPY files/${UV_ARCHIVE_FILENAME} .

# 安装 uv
RUN tar -xzf ${UV_ARCHIVE_FILENAME} && \
    # 假设解压后可执行文件名为 uv (通常是这样)
    mv uv-x86_64-unknown-linux-musl/uv /usr/local/bin/uv && \
    chmod +x /usr/local/bin/uv

RUN uv --version





ENV NODE_VERSION 22.16.0
ENV NODE_ARCHIVE_NAME node-v${NODE_VERSION}-linux-x64.tar.xz
ENV NODE_EXTRACTED_DIR_NAME node-v${NODE_VERSION}-linux-x64

# Node.js 将被安装到的目录
ENV NODE_INSTALL_PATH /usr/local/lib/nodejs


# 2. 将本地的 Node.js 压缩包复制到镜像中的临时位置
COPY files/${NODE_ARCHIVE_NAME} /tmp/${NODE_ARCHIVE_NAME}
RUN rpm -qa |grep glibc
# 3. 创建 Node.js 安装目录，解压并创建软链接
RUN mkdir -p ${NODE_INSTALL_PATH} && \
    # 解压到指定目录
    tar -xJf /tmp/${NODE_ARCHIVE_NAME} -C ${NODE_INSTALL_PATH} && \
    # 创建软链接到 /usr/local/bin，使其在 PATH 中可用
    ln -s ${NODE_INSTALL_PATH}/${NODE_EXTRACTED_DIR_NAME}/bin/node /usr/local/bin/node && \
    ln -s ${NODE_INSTALL_PATH}/${NODE_EXTRACTED_DIR_NAME}/bin/npm /usr/local/bin/npm && \
    ln -s ${NODE_INSTALL_PATH}/${NODE_EXTRACTED_DIR_NAME}/bin/npx /usr/local/bin/npx && \
    # 清理临时压缩包
    rm /tmp/${NODE_ARCHIVE_NAME}

# 4. 验证安装 (可选，但推荐)
RUN node -v && \
    npm -v && \
    npx --version




# 验证安装

#RUN  git clone https://github.com/antvis/mcp-server-chart
RUN npm install -g @antv/mcp-server-chart
