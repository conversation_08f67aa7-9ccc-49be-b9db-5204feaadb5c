# 项目结构
```tree
├── APP-META (构建用到的文件)
│    └── docker-config (构建镜像时的工作目录)
│        ├── Dockerfile          # 生产环境Dockerfile
│        ├── Dockerfile_daily    # 日常环境Dockerfile
│        ├── Dockerfile_staging  # 预发环境Dockerfile
│        ├── script_legacy/      # 旧脚本目录（向后兼容）
│        └── environment
│            └── common
│                ├── base (要使用aone启动应用，必须包含的脚本/配置)
│                │   ├── app
│                │   │   └── bin
│                │   │       ├── appctl.sh
│                │   │       ├── health.sh
│                │   │       ├── setenv.sh
│                │   │       ├── start.sh
│                │   │       └── stop.sh
│                │   └── cai
│                │       ├── bin
│                │       │   └── nginxctl
│                │       └── conf
│                │           └── nginx-proxy.conf
│                └── custom (项目自定义的脚本/配置)
│                    └── app
│                        └── bin
│                            ├── health.sh
│                            ├── setenv.sh
│                            ├── start.sh
│                            └── stop.sh
├── ecs-mcp.release (构建文件)
├── conf (配置文件)
│    ├── circus.ini
│    └── requirements.txt
├── src (项目代码)
│    └── main.py
└── 启动必看 - 不看部署必挂.md
```
# 重要的事情说三遍

## 定制启动脚本请写到APP-META/.../common/custom目录下!!!
## 定制启动脚本请写到APP-META/.../common/custom目录下!!!
## 定制启动脚本请写到APP-META/.../common/custom目录下!!!

APP-META/.../common/custom目录中存储了健康检查(health.sh)、环境变量(setenv.sh)、启动(start.sh)、停止(stop.sh)等脚本，这些脚本会在容器启动时被执行。如果项目有特殊需求，可以在custom目录下修改对应的脚本，不要直接修改base目录下的脚本，否则会导致项目无法正常启动。

## 脚本目录说明

项目脚本已经重构为更清晰的目录结构，位于项目根目录下的 `scripts` 目录：

- `scripts/`: 脚本目录，按功能模块组织
  - `backend/`: 后端服务管理脚本
    - `health/`: 后端健康检查脚本
    - `nginx/`: Nginx配置管理脚本
  - `common/`: 通用工具和共享代码
  - `tests/`: 测试脚本
  - `cli/`: 命令行接口

详细说明请参考各目录下的README.md文件。

# 本地启动
