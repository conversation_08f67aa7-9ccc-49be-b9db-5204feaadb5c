// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { StarFilledIcon, GitHubLogoIcon } from "@radix-ui/react-icons";
import Link from "next/link";

// 在 "next/link" 和第一个 "~/" 导入之间添加一个空行
import { NumberTicker } from "~/components/magicui/number-ticker";
import { Button } from "~/components/ui/button";
import { UserInfoDisplay } from "~/components/ui/UserInfoDisplay";
import { env } from "~/env";

// StarCounter 仍然是一个 async Server Component，可以正常工作
export async function StarCounter() {
  let stars = 1000;
  try {
    const response = await fetch(
      "https://api.github.com/repos/bytedance/deer-flow",
      {
        headers: env.GITHUB_OAUTH_TOKEN
          ? {
            Authorization: `Bearer ${env.GITHUB_OAUTH_TOKEN}`,
            "Content-Type": "application/json",
          }
          : {},
        next: {
          revalidate: 3600,
        },
      },
    );
    if (response.ok) {
      const data = await response.json();
      stars = data.stargazers_count ?? stars;
    } else {
      console.error("Failed to fetch GitHub stars, status:", response.status);
    }
  } catch (error) {
    console.error("Error fetching GitHub stars:", error);
  }
  return (
    <>
      <StarFilledIcon className="size-4 transition-colors duration-300 group-hover:text-yellow-500" />
      {stars != null && (
        <NumberTicker className="font-mono tabular-nums" value={stars} />
      )}
    </>
  );
}


export async function SiteHeader() {
  // const userInfo = await fetchUserInfo(); // 关键：移除这里的调用

  return (
    <header className="supports-backdrop-blur:bg-background/80 bg-background/40 sticky top-0 left-0 z-40 flex h-15 w-full flex-col items-center backdrop-blur-lg">
      <div className="container flex h-15 items-center justify-between px-3">
        <div className="text-xl font-medium">
          <span className="mr-1 text-2xl">🦌</span>
          <span>CloudBot智能诊断</span>
        </div>
        <div className="relative flex items-center">
          <div
            className="pointer-events-none absolute inset-0 z-0 h-full w-full rounded-full opacity-60 blur-2xl"
            style={{
              background: "linear-gradient(90deg, #ff80b5 0%, #9089fc 100%)",
              filter: "blur(32px)",
            }}
          />
          <Button
            variant="outline"
            size="sm"
            asChild
            className="group relative z-10"
          >
            <Link href="https://code.alibaba-inc.com/cloud-ecs-devops/ecs-deep-diagnose" target="_blank">
              <GitHubLogoIcon className="size-4" />
              Code Repo
              {/* StarCounter 是一个服务器组件，可以继续在这里使用 */}
              {env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY &&
                env.GITHUB_OAUTH_TOKEN && <StarCounter />}
            </Link>
          </Button>

          {/* 关键：使用新的客户端组件来显示用户信息 */}
          <UserInfoDisplay />
        </div>
      </div>
      <hr className="from-border/0 via-border/70 to-border/0 m-0 h-px w-full border-none bg-gradient-to-r" />
    </header>
  );
}
