// app/components/UserInfoDisplay.tsx
"use client"; // 关键：标记为客户端组件

import { useEffect, useState } from "react";
import { Avatar, Tooltip } from 'antd';
import type { UserInfo } from "~/lib/userInfo"; // <--- 使用 import type 导入类型
import { defaultUserInfo, fetchUserInfo } from "~/lib/userInfo"; // <--- 使用常规 import 导入值

export function UserInfoDisplay() {
  const [userInfo, setUserInfo] = useState<UserInfo>(defaultUserInfo);
  const [isLoading, setIsLoading] = useState(true); // 可选：添加加载状态

  useEffect(() => {
    async function loadUserInfo() {
      setIsLoading(true);
      const fetchedData = await fetchUserInfo(); // 在客户端运行时执行
      setUserInfo(fetchedData);
      setIsLoading(false);
    }
    loadUserInfo();
  }, []); // 空依赖数组，仅在组件挂载时执行一次

  // 可选：显示加载状态
  if (isLoading && userInfo.id === "unknown") { // 只有在初始加载时显示
    return (
      <div className="flex items-center">
        <Avatar size="large" style={{ cursor: 'pointer', marginLeft: '12px' }} />
        <span style={{ fontSize: 14, color: '#aaa', marginLeft: 4 }}>加载中...</span>
      </div>
    );
  }

  return (
    <>
      <Tooltip title={userInfo.id + '-' + (userInfo.nickNameCn ?? userInfo.realname)}>
        <Avatar src={userInfo.emailAddr} size="large" style={{ cursor: 'pointer', marginLeft: '12px' }} />
      </Tooltip>
      <span style={{ fontSize: 14, color: '#fff', marginLeft: 4 }}>
        {userInfo.nickNameCn ?? userInfo.realname}
      </span>
    </>
  );
}
