# 🔍 ECS-Deep-Diagnose Web UI

This is the web UI for ECS-Deep-Diagnose.

## Quick Start

### Prerequisites

- ECS-Deep-Diagnose
- Node.js (v22.14.0+)
- pnpm (v10.6.2+) as package manager

### Configuration

Create a `.env` file in the project root and configure the following environment variables:

- `NEXT_PUBLIC_API_URL`: The URL of the ecs-deep-diagnose API.

It's always a good idea to start with the given example file, and edit the `.env` file with your own values:

```bash
cp .env.example .env
```

## How to Install

ECS-Deep-Diagnose Web UI uses `pnpm` as its package manager.
To install the dependencies, run:

```bash
cd web
pnpm install
```

## How to Run in Development Mode

> [!NOTE]
> Ensure the Python API service is running before starting the web UI.

Start the web UI development server:

```bash
cd web
pnpm dev
```

By default, the web UI will be available at `http://localhost:3000`.

You can set the `NEXT_PUBLIC_API_URL` environment variable if you're using a different host or location.

```ini
# .env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

## Docker

You can also run this project with Docker.

First, you need read the [configuration](#configuration) below. Make sure `.env` file is ready.

Second, to build a Docker image of your own web server:

```bash
docker build --build-arg NEXT_PUBLIC_API_URL=YOUR_ECS-DEEP-DIAGNOSE_API -t ecs-deep-diagnose-web .
```

Final, start up a docker container running the web server:

```bash
# Replace ecs-deep-diagnose-web-app with your preferred container name
docker run -d -t -p 3000:3000 --env-file .env --name ecs-deep-diagnose-web-app ecs-deep-diagnose-web

# stop the server
docker stop ecs-deep-diagnose-web-app
```

### Docker Compose

You can also setup this project with the docker compose:

```bash
# building docker image
docker compose build

# start the server
docker compose up
```

## License

This project is available under the MIT License.
