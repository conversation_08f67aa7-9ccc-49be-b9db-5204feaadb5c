import json

from langchain_core.utils.function_calling import convert_to_openai_tool
from langchain_mcp_adapters.client import MultiServerMCPClient


def transform_server_dict(original_dict: dict) -> dict:
    """
    Transforms the original server dictionary to the target format,
    keeping only 'transport' and 'url' for each server.

    Args:
      original_dict: The input dictionary with the original server data.

    Returns:
      A new dictionary with the transformed server data.
    """
    transformed_dict = {}
    if "servers" in original_dict and isinstance(original_dict["servers"], dict):
        for server_name, server_details in original_dict["servers"].items():
            # Ensure server_details is a dictionary before trying to get items from it
            if isinstance(server_details, dict):
                if server_details.get("command") and server_details.get("transport", "") == "stdio":
                    transformed_dict[server_name] = {
                        "transport": server_details.get("transport"),
                        "command": server_details.get("command"),
                        "args": server_details.get("args"),  # args: list[str],
                        "env": server_details.get("env"),  # env: dict[str, str] | None
                        "cwd": server_details.get("cwd")  # cwd: str | Path | None
                    }
                else:
                    transformed_dict[server_name] = {
                        "transport": server_details.get("transport"),
                        "url": server_details.get("url"),
                        "headers":server_details.get("headers")
                    }
            else:
                # Handle cases where a server entry might not be a dictionary
                # (though not expected based on the example)
                transformed_dict[server_name] = {
                    "transport": None,
                    "url": None
                }
    return transformed_dict
async def get_mcp_server_descriptions(mcp_settings) -> str:

    mcp_client=MultiServerMCPClient(transform_server_dict(mcp_settings))
    tools=await mcp_client.get_tools()
    tool_description_list=[]
    for tool in tools:
        schema = json.dumps(convert_to_openai_tool(tool, strict=True)['function']['parameters'],
                            indent=2, ensure_ascii=False)
        schema_indented = '\n'.join(f'    {line}' for line in schema.splitlines())
        schema_str = f'    Input Schema:\n{schema_indented}'
        tool_description = f'- {tool.name}: {tool.description}\n{schema_str}'
        tool_description_list.append(tool_description)

    return "## Available Tools\n"+'\n\n'.join(tool_description_list)
