

from langgraph.prebuilt import create_react_agent

from prompts import apply_prompt_template
from llms.llm import get_llm_by_type
from config.settings.agents import AGENT_LLM_MAP
from graph.types import State
from tools.mcp_tools import get_mcp_server_descriptions

async def update(state:State,configurable):
    state["mcp_servers_description"] = await get_mcp_server_descriptions(configurable.mcp_settings)

    return state
# Create agents using configured LLM types
async def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str,configurable):
    """Factory function to create agents with consistent configuration."""

    async def generate_prompt_async(current_agent_state: State):
        # 'configurable' 和 'prompt_template' 来自 create_agent 函数的闭包作用域
        # 'current_agent_state' 是 LangGraph 在调用 prompt runnable 时传入的实际状态

        # 首先，异步更新状态
        # 注意：update 函数会修改 current_agent_state 并返回它
        updated_state = await update(current_agent_state, configurable)

        # 然后，使用更新后的状态和模板来应用提示
        # apply_prompt_template 应该是一个同步函数
        return apply_prompt_template(prompt_template, updated_state)
    return create_react_agent(
        name=agent_name,
        model=get_llm_by_type(AGENT_LLM_MAP[agent_type]),
        tools=tools,
        prompt=generate_prompt_async,
    )
