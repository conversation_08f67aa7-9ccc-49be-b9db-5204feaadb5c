"""
Environment variable configuration loader.
"""

import os
from typing import Dict, Any, Optional, Union, Type
from ..core.base_config import BaseConfig


class EnvConfigLoader(BaseConfig):
    """
    Environment variable configuration loader.
    Loads configuration from environment variables with type conversion.
    """
    
    def __init__(self, prefix: str = ""):
        super().__init__()
        self.prefix = prefix
    
    def load(self) -> Dict[str, Any]:
        """
        Load configuration from environment variables.
        
        Returns:
            Dict[str, Any]: Configuration data from environment variables
        """
        config = {}
        
        for key, value in os.environ.items():
            if self.prefix and not key.startswith(self.prefix):
                continue
            
            # Remove prefix if specified
            config_key = key[len(self.prefix):] if self.prefix else key
            config[config_key.lower()] = value
        
        return config
    
    def get_env(self, key: str, default: Any = None, type_cast: Optional[Type] = None) -> Any:
        """
        Get environment variable with optional type casting.
        
        Args:
            key: Environment variable key
            default: Default value if not found
            type_cast: Type to cast the value to
            
        Returns:
            Any: Environment variable value
        """
        full_key = f"{self.prefix}{key}" if self.prefix else key
        value = os.getenv(full_key, default)
        
        if value is None or type_cast is None:
            return value
        
        try:
            if type_cast == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif type_cast == int:
                return int(value)
            elif type_cast == float:
                return float(value)
            else:
                return type_cast(value)
        except (ValueError, TypeError):
            return default
    
    def get_list(self, key: str, separator: str = ",", default: Optional[list] = None) -> list:
        """
        Get environment variable as a list.
        
        Args:
            key: Environment variable key
            separator: List item separator
            default: Default value if not found
            
        Returns:
            list: List of values
        """
        value = self.get_env(key)
        if value is None:
            return default or []
        
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def get_dict(self, key: str, item_separator: str = ",", kv_separator: str = "=", 
                 default: Optional[dict] = None) -> dict:
        """
        Get environment variable as a dictionary.
        
        Args:
            key: Environment variable key
            item_separator: Separator between key-value pairs
            kv_separator: Separator between key and value
            default: Default value if not found
            
        Returns:
            dict: Dictionary of values
        """
        value = self.get_env(key)
        if value is None:
            return default or {}
        
        result = {}
        for item in value.split(item_separator):
            if kv_separator in item:
                k, v = item.split(kv_separator, 1)
                result[k.strip()] = v.strip()
        
        return result
