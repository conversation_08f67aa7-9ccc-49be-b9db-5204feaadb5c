"""
Security configuration management.
"""

import os
from typing import Optional
from common.security.keycenter.keycenterclient import get_keycenter_client


class SecurityConfig:
    """
    Security configuration manager.
    Handles encrypted secrets and KeyCenter integration.
    """
    
    def __init__(self):
        self._client = None
    
    @property
    def client(self):
        """Get KeyCenter client instance."""
        if self._client is None:
            self._client = get_keycenter_client()
        return self._client
    
    def decrypt_secret(self, encrypted_secret: str, pub_name: str = "ecs-deep-diagnose_aone_key") -> str:
        """
        Decrypt an encrypted secret using KeyCenter.
        
        Args:
            encrypted_secret: The encrypted secret string
            pub_name: KeyCenter public key name
            
        Returns:
            str: Decrypted secret
        """
        result = self.client.decrypt(pub_name, encrypted_secret).decode('utf-8')
        
        # Clean up control characters that might be added during encryption
        result = (result.rstrip('\x02')
                       .rstrip('\x08')
                       .rstrip('\x10')
                       .rstrip('\x06')
                       .rstrip('\x04')
                       .rstrip('\x05')
                       .rstrip('\x0c')
                       .strip())
        
        return result
    
    def get_environment_secret(self, env_var_name: str, default: Optional[str] = None) -> Optional[str]:
        """
        Get secret from environment variable.
        
        Args:
            env_var_name: Environment variable name
            default: Default value if not found
            
        Returns:
            str: Secret value or default
        """
        return os.getenv(env_var_name, default)


# Global security config instance
security_config = SecurityConfig()
