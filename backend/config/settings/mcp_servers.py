"""
MCP (Model Context Protocol) server configuration.
"""

import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server."""
    name: str
    transport: str
    enabled_tools: List[str]
    add_to_agents: List[str]
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    command: Optional[str] = None
    args: Optional[List[str]] = None
    headers: Optional[Dict[str, str]] = None


def get_default_headers() -> Dict[str, str]:
    """
    Get default headers for MCP requests.
    
    Note: In production, this should be loaded from encrypted configuration.
    """
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('MCP_AUTH_TOKEN', 'M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=')}"
    }


def get_mcp_settings() -> Dict[str, Any]:
    """
    Get MCP server settings configuration.
    
    Returns:
        Dict containing MCP server configurations
    """
    headers = get_default_headers()
    
    return {
        "servers": {
            "cloudbot": MCPServerConfig(
                name="cloudbot",
                transport="streamable_http",
                url=os.getenv("CLOUDBOT_MCP_URL", "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/"),
                enabled_tools=[
                    "getVmBasicInfo",
                    "getNcBasicInfo", 
                    "runDiagnose",
                    "listReportedOperationalEvents",
                    "listMonitorExceptions",
                    "listActionTrail",
                    "ScreenShotDiagnose",
                    "listOperationRecords",
                    "listChangeRecords",
                    "SubmitOps",
                ],
                add_to_agents=["researcher"],
                headers=headers
            ).__dict__,
            
            "vm_coredump": MCPServerConfig(
                name="vm_coredump",
                transport="streamable_http",
                url=os.getenv("VM_COREDUMP_MCP_URL", "http://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/?token=************************************************"),
                enabled_tools=["get_vm_coredump"],
                add_to_agents=["researcher"]
            ).__dict__,
            
            "antv": MCPServerConfig(
                name="antv",
                transport="stdio",
                command="npx",
                args=["-y", "@antv/mcp-server-chart"],
                enabled_tools=["generate_pie_chart"],
                add_to_agents=["researcher"]
            ).__dict__
        }
    }


# For backward compatibility, provide the old format
def get_legacy_mcp_settings() -> Dict[str, Any]:
    """
    Get MCP settings in the legacy format for backward compatibility.
    """
    return get_mcp_settings()


# For backward compatibility
def get_legacy_headers() -> Dict[str, str]:
    """
    Get headers in the legacy format for backward compatibility.
    """
    return get_default_headers()
