app:
  port: 8000
  secret: ecs_deep_diagnose_secret
  buc_host: https://login.alibaba-inc.com

url:
  ecs_deep_diagnose_endpoint: https://https://ecs-deep-diagnose.aliyun-inc.com/


keys:
  # secret config
  keycenter_pub_name: &keycenter_pub_name ecs-ai-middleoffice-key


langfuse:
  public_key:  !decrypt [ *langfuse_public_key, *keycenter_pub_name ]
  secret_key:  !decrypt [ *langfuse_secret_key, *keycenter_pub_name ]
  secret_key_public_evaluation: !decrypt [ *langfuse_secret_key_public_evaluation, *keycenter_pub_name ]
  public_key_public_evaluation: !decrypt [ *langfuse_public_key_public_evaluation, *keycenter_pub_name ]

  user_name: <EMAIL>
  password:   !decrypt [ *langfuse_admin_password, *keycenter_pub_name ]

llm:
  tongyi:
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    secret: !decrypt [ *tongyi_sk, *keycenter_pub_name ]
