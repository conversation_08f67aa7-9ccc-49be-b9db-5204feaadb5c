

import json
import pytest
import httpx
import asyncio
import uuid

# 集成测试：直接测试运行在localhost:8000上的服务
# 运行测试前，请确保服务已在localhost:8000端口启动
# 可以通过以下命令启动服务：
# cd backend && python server.py


@pytest.mark.asyncio
async def test_chat_stream_api():
    """测试聊天流式API - 集成测试，直接连接到localhost:8000"""
    # 创建一个异步客户端，直接连接到本地运行的服务
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        # 构建请求数据
        request_data = {
            "messages": [
                {
                    "role": "user",
                    "content": "查询这个实例i-t4nivj10b4kcfzoy72e6的实例规格信息?"
                }
            ],
            "thread_id": str(uuid.uuid4()),  # 使用随机生成的thread_id
            "auto_accepted_plan": False,
            "enable_background_investigation": False,
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "max_search_results": 3
        }
        
        # 发送POST请求到聊天流式API
        response = await client.post(
            "/api/chat/stream",
            json=request_data,
            headers={
                "sec-ch-ua-platform": "macOS",
                "Cache-Control": "no-cache",
                "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "Content-Type": "application/json",
                "sec-ch-ua-mobile": "?0"
            }
        )
        
        # 验证响应状态码
        assert response.status_code == 200
        
        # 验证响应内容类型
        assert response.headers["content-type"] == "text/event-stream"
        
        # 读取流式响应内容
        response_text = ""
        async for chunk in response.aiter_text():
            response_text += chunk
            # 如果收到足够的数据，可以提前退出
            if len(response_text) > 100:
                break
        
        # 验证响应内容包含预期的数据格式
        assert "data:" in response_text
        
        # 尝试解析一个事件数据
        event_data = None
        for line in response_text.split("\n"):
            if line.startswith("data:"):
                try:
                    event_data = json.loads(line[5:].strip())
                    break
                except json.JSONDecodeError:
                    continue
        
        # 验证事件数据结构
        assert event_data is not None
        assert "thread_id" in event_data
        assert event_data["thread_id"] == "dzlRceJHRNmS0RavMkxOz"


@pytest.mark.asyncio
async def test_chat_stream_api_full_response():
    """测试聊天流式API完整响应 - 集成测试，直接连接到localhost:8000"""
    # 创建一个异步客户端，直接连接到本地运行的服务
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        # 构建请求数据
        request_data = {
            "messages": [
                {
                    "role": "user",
                    "content": "查询这个实例i-t4nivj10b4kcfzoy72e6的实例规格信息?"
                }
            ],
            "thread_id": str(uuid.uuid4()),  # 使用随机生成的thread_id
            "auto_accepted_plan": False,
            "enable_background_investigation": False,
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "max_search_results": 3
        }
        
        # 发送POST请求到聊天流式API
        response = await client.post(
            "/api/chat/stream",
            json=request_data,
            headers={
                "sec-ch-ua-platform": "macOS",
                "Cache-Control": "no-cache",
                "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "Content-Type": "application/json",
                "sec-ch-ua-mobile": "?0"
            }
        )
        
        # 验证响应状态码
        assert response.status_code == 200
        
        # 验证响应内容类型
        assert response.headers["content-type"] == "text/event-stream"
        
        # 读取并验证流式响应内容 - 获取完整响应
        response_chunks = []
        async for chunk in response.aiter_text():
            response_chunks.append(chunk)
        
        # 合并所有响应块
        full_response = "".join(response_chunks)
        
        # 验证响应包含预期的数据格式
        assert "data:" in full_response
        
        # 解析所有事件数据
        events = []
        for line in full_response.split("\n"):
            if line.startswith("data:"):
                try:
                    event_data = json.loads(line[5:].strip())
                    events.append(event_data)
                except json.JSONDecodeError:
                    continue
        
        # 验证至少收到了一个事件
        assert len(events) > 0
        
        # 验证事件数据结构
        for event in events:
            assert "thread_id" in event
            assert event["thread_id"] == request_data["thread_id"]
            
        # 验证响应中包含了某些关键词（这里我们期望响应中包含一些关于ECS实例的信息）
        assert any("实例" in event.get("content", "") for event in events if "content" in event)
