"""
Unit tests for common.constants module.
"""

import pytest
from unittest.mock import patch

from common.constants import (
    APP_HOME_DIR,
    DEFAULT_MAX_STEP_NUM,
    DEFAULT_AUTO_ACCEPTED_PLAN,
    DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
    headers,
    mcp_settings
)


class TestAppConstants:
    """Test application-level constants."""
    
    def test_app_home_dir_is_string(self):
        """Test APP_HOME_DIR is a valid string."""
        assert isinstance(APP_HOME_DIR, str)
        assert len(APP_HOME_DIR) > 0
        assert APP_HOME_DIR == '/home/<USER>/ecs-deep-diagnose'


class TestWorkflowConstants:
    """Test workflow-related constants."""
    
    def test_default_max_step_num(self):
        """Test DEFAULT_MAX_STEP_NUM is valid."""
        assert isinstance(DEFAULT_MAX_STEP_NUM, int)
        assert DEFAULT_MAX_STEP_NUM > 0
        assert DEFAULT_MAX_STEP_NUM == 20
        
    def test_default_auto_accepted_plan(self):
        """Test DEFAULT_AUTO_ACCEPTED_PLAN is boolean."""
        assert isinstance(DEFAULT_AUTO_ACCEPTED_PLAN, bool)
        assert DEFAULT_AUTO_ACCEPTED_PLAN is True
        
    def test_default_enable_background_investigation(self):
        """Test DEFAULT_ENABLE_BACKGROUND_INVESTIGATION is boolean."""
        assert isinstance(DEFAULT_ENABLE_BACKGROUND_INVESTIGATION, bool)
        assert DEFAULT_ENABLE_BACKGROUND_INVESTIGATION is False


class TestMCPConfiguration:
    """Test MCP (Model Context Protocol) configuration."""
    
    def test_headers_structure(self):
        """Test headers dictionary structure."""
        assert isinstance(headers, dict)
        assert "Content-Type" in headers
        assert "Authorization" in headers
        assert headers["Content-Type"] == "application/json"
        assert headers["Authorization"].startswith("Bearer ")
        
    def test_mcp_settings_structure(self):
        """Test mcp_settings dictionary structure."""
        assert isinstance(mcp_settings, dict)
        assert "servers" in mcp_settings
        assert isinstance(mcp_settings["servers"], dict)
        
    def test_mcp_servers_configuration(self):
        """Test individual MCP server configurations."""
        servers = mcp_settings["servers"]
        
        # Test that we have expected servers
        expected_servers = ["cloudbot", "vm_coredump", "antv"]
        for server_name in expected_servers:
            assert server_name in servers
            
        # Test server configuration structure
        for server_name, server_config in servers.items():
            assert isinstance(server_config, dict)
            
            # Required fields
            required_fields = ["name", "transport", "enabled_tools", "add_to_agents"]
            for field in required_fields:
                assert field in server_config, f"Missing {field} in {server_name}"
                
            # Test field types
            assert isinstance(server_config["name"], str)
            assert isinstance(server_config["transport"], str)
            assert isinstance(server_config["enabled_tools"], list)
            assert isinstance(server_config["add_to_agents"], list)
            
            # Test that enabled_tools is not empty
            assert len(server_config["enabled_tools"]) > 0
            
            # Test that add_to_agents contains valid agent names
            valid_agents = ["researcher", "coordinator", "planner", "coder", "reporter"]
            for agent in server_config["add_to_agents"]:
                # Note: We're being flexible here as the actual agent list might vary
                assert isinstance(agent, str)
                assert len(agent) > 0
                
    def test_cloudbot_server_specific(self):
        """Test cloudbot server specific configuration."""
        cloudbot = mcp_settings["servers"]["cloudbot"]
        
        assert cloudbot["name"] == "cloudbot"
        assert cloudbot["transport"] == "streamable_http"
        assert "url" in cloudbot
        assert cloudbot["url"].startswith("http")
        assert "headers" in cloudbot
        assert cloudbot["headers"] == headers
        
        # Test specific tools
        expected_tools = [
            "getVmBasicInfo", "getNcBasicInfo", "runDiagnose",
            "listReportedOperationalEvents", "listMonitorExceptions",
            "listActionTrail", "ScreenShotDiagnose", "listOperationRecords",
            "listChangeRecords", "SubmitOps"
        ]
        for tool in expected_tools:
            assert tool in cloudbot["enabled_tools"]
            
    def test_vm_coredump_server_specific(self):
        """Test vm_coredump server specific configuration."""
        vm_coredump = mcp_settings["servers"]["vm_coredump"]
        
        assert vm_coredump["name"] == "vm_coredump"
        assert vm_coredump["transport"] == "streamable_http"
        assert "url" in vm_coredump
        assert vm_coredump["url"].startswith("http")
        assert "get_vm_coredump" in vm_coredump["enabled_tools"]
        
    def test_antv_server_specific(self):
        """Test antv server specific configuration."""
        antv = mcp_settings["servers"]["antv"]
        
        assert antv["name"] == "antv"
        assert antv["transport"] == "stdio"
        assert "command" in antv
        assert antv["command"] == "npx"
        assert "args" in antv
        assert isinstance(antv["args"], list)
        assert "generate_pie_chart" in antv["enabled_tools"]


class TestConstantsIntegration:
    """Test integration aspects of constants."""
    
    def test_constants_importable(self):
        """Test that all constants can be imported without errors."""
        # This test passes if the imports at the top of the file work
        assert True
        
    def test_constants_not_none(self):
        """Test that critical constants are not None."""
        critical_constants = [
            APP_HOME_DIR,
            DEFAULT_MAX_STEP_NUM,
            DEFAULT_AUTO_ACCEPTED_PLAN,
            DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
            headers,
            mcp_settings
        ]
        
        for constant in critical_constants:
            assert constant is not None
            
    def test_mcp_settings_serializable(self):
        """Test that mcp_settings can be serialized (important for config passing)."""
        import json
        
        # Should not raise an exception
        json_str = json.dumps(mcp_settings)
        assert isinstance(json_str, str)
        assert len(json_str) > 0
        
        # Should be able to deserialize back
        deserialized = json.loads(json_str)
        assert deserialized == mcp_settings
