import inspect
import os
from typing import Optional, Any, cast, AsyncIterator, List, Dict, Iterator, Mapping, Type

from dashscope.tokenizers.qwen_tokenizer import QwenTokenizer
from dashscope.tokenizers.tokenizer_base import Tokenizer
from langchain.globals import get_llm_cache
from langchain_core.caches import BaseCache
from langchain_core.callbacks import Async<PERSON>allbackManagerForLLMRun, CallbackManagerForLLMRun
from langchain_core.language_models.chat_models import _gen_info_and_msg_metadata
from langchain_core.load import dumps
from langchain_core.messages import (

    AIMessageChunk,

    BaseMessage,

    BaseMessageChunk,

    ChatMessageChunk,

    FunctionMessageChunk,

    HumanMessageChunk,

    SystemMessageChunk,

    ToolMessageChunk,

)
from langchain_core.messages.ai import UsageMetadata, InputTokenDetails, OutputTokenDetails
from langchain_core.messages.tool import tool_call_chunk
from langchain_core.outputs import Cha<PERSON><PERSON><PERSON><PERSON>, ChatGenerationChunk, ChatGeneration
from langchain_core.runnables import run_in_executor
from langchain_core.utils.pydantic import is_basemodel_subclass
from langchain_openai import ChatOpenAI
from langchain_openai.chat_models.base import _convert_dict_to_message

current_path = os.path.dirname(os.path.abspath(__file__))

root_path = os.path.dirname(current_path)





class ChatQwen(ChatOpenAI):

    """LLM基类重构版，提供以下增强功能：

    - 统一异步调用接口

    - 内置fallback机制

    - 支持多模态输入

    """

    extra_body: Dict = {"enable_thinking": False}



    def get_token_ids(self, text: str) -> List[int]:

        # dashscope 暂不支持 deepseek tokenizer, 所以使用默认qwen tokenizer

        tokenizer: Tokenizer = QwenTokenizer(

            os.path.join(root_path, 'resources', 'qwen.tiktoken'))

        tokens: List[int] = tokenizer.encode(text)

        return tokens



    async def _agenerate_with_cache(

            self,

            messages: list[BaseMessage],

            stop: Optional[list[str]] = None,

            run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,

            **kwargs: Any,

    ) -> ChatResult:

        llm_cache = self.cache if isinstance(self.cache, BaseCache) else get_llm_cache()

        # We should check the cache unless it's explicitly set to False

        # A None cache means we should use the default global cache

        # if it's configured.

        check_cache = self.cache or self.cache is None

        if check_cache:

            if llm_cache:

                llm_string = self._get_llm_string(stop=stop, **kwargs)

                prompt = dumps(messages)

                cache_val = await llm_cache.alookup(prompt, llm_string)

                if isinstance(cache_val, list):

                    return ChatResult(generations=cache_val)

            elif self.cache is None:

                pass

            else:

                msg = "Asked to cache, but no cache found at `langchain.cache`."

                raise ValueError(msg)



        # Apply the rate limiter after checking the cache, since

        # we usually don't want to rate limit cache lookups, but

        # we do want to rate limit API requests.

        if self.rate_limiter:

            await self.rate_limiter.aacquire(blocking=True)



        # If stream is not explicitly set, check if implicitly requested by

        # astream_events() or astream_log(). Bail out if _astream not implemented

        if self._should_stream(

                async_api=True,

                run_manager=run_manager,

                stream=True,

                **kwargs,

        ):

            chunks: list[ChatGenerationChunk] = []

            async for chunk in self._astream(messages, stop=stop, **kwargs):

                chunk.message.response_metadata = _gen_info_and_msg_metadata(chunk)

                if run_manager:

                    if chunk.message.id is None:

                        chunk.message.id = f"run-{run_manager.run_id}"

                    """

                    original code: 

                        await run_manager.on_llm_new_token(

                            cast(str, chunk.message.content), chunk=chunk

                        )

                    NOTE: add metadata kwargs support

                    """

                    await run_manager.on_llm_new_token(

                        cast(str, chunk.message.content), chunk=chunk, metadata=run_manager.metadata

                    )

                chunks.append(chunk)

            result = self._generate_from_stream(iter(chunks))

        else:

            if inspect.signature(self._agenerate).parameters.get("run_manager"):

                result = await self._agenerate(

                    messages, stop=stop, run_manager=run_manager, extra_body=self.extra_body, **kwargs

                )

            else:

                result = await self._agenerate(messages, stop=stop, extra_body=self.extra_body, **kwargs)



        # Add response metadata to each generation

        for idx, generation in enumerate(result.generations):

            if run_manager and generation.message.id is None:

                generation.message.id = f"run-{run_manager.run_id}-{idx}"

            generation.message.response_metadata = _gen_info_and_msg_metadata(

                generation

            )

        if len(result.generations) == 1 and result.llm_output is not None:

            result.generations[0].message.response_metadata = {

                **result.llm_output,

                **result.generations[0].message.response_metadata,

            }

        if check_cache and llm_cache:

            await llm_cache.aupdate(prompt, llm_string, result.generations)

        return result



    def _generate(

            self,

            messages: List[BaseMessage],

            stop: Optional[List[str]] = None,

            run_manager: Optional[CallbackManagerForLLMRun] = None,

            **kwargs: Any,

    ) -> ChatResult:

        """

        生成响应消息。



        根据输入条件选择本地或DashScope的Deepseek模型进行生成。



        Args:

            query_messages (List[BaseMessage]): 查询消息列表。

            stop (Optional[List[str]], optional): 停止令牌列表，默认为None。

            run_manager (Optional[CallbackManagerForLLMRun], optional): 运行管理器，默认为None。

            **kwargs (Any): 额外的关键字参数。



        Returns:

            ChatResult: 生成的聊天结果。

        """

        # self._rebuild_images_messages(messages, kwargs)
        #
        # from model_io.llm import LanguageModelName  # 防止循环引用

        # TODO: QWQ 模型暂不支持非流式生成， 暂时手动适配。 后续需要优化。

        if self.model_name.startswith("qwen3"):

            payload = self._get_request_payload(messages, stop=stop, stream=True, **kwargs)

            response = self.client.create(**payload)

            chat_result = None

            for chunk in response:

                if not chat_result:

                    result_dict = {

                        "id": chunk.id,

                        **(chunk.choices[0].delta.model_dump())

                    }

                    chat_result = ChatResult(

                        generations=[

                            ChatGeneration(

                                message=_convert_dict_to_message(result_dict),

                                text=""

                            )

                        ],

                        llm_output={

                            "token_usage": None,

                            "model_name": self.model_name,

                            "system_fingerprint": "",

                        }

                    )

                else:

                    content = chunk.choices[0].delta.content

                    if content:

                        chat_result.generations[0].message.content += content

                        chat_result.generations[0].text += content

                if chunk.usage:

                    chat_result.llm_output["token_usage"] = chunk.usage

            return chat_result

        else:

            return super()._generate(

                messages, stop, run_manager=run_manager, extra_body=self.extra_body, **kwargs

            )



    def _stream(

            self,

            messages: List[BaseMessage],

            stop: Optional[List[str]] = None,

            run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,

            **kwargs: Any,

    ) -> Iterator[ChatGenerationChunk]:

        #self._rebuild_images_messages(messages, kwargs)



        kwargs["stream"] = True

        payload = self._get_request_payload(messages, stop=stop, extra_body=self.extra_body, **kwargs)

        default_chunk_class: Type[BaseMessageChunk] = AIMessageChunk

        base_generation_info = {}



        if "response_format" in payload and is_basemodel_subclass(

                payload["response_format"]

        ):

            # TODO: Add support for streaming with Pydantic response_format.

            # warnings.warn("Streaming with Pydantic response_format not yet supported.")

            chat_result = self._generate(

                messages, stop, run_manager=run_manager, **kwargs

            )

            msg = chat_result.generations[0].message

            yield ChatGenerationChunk(

                message=AIMessageChunk(

                    **msg.dict(exclude={"type", "additional_kwargs"}),

                    # preserve the "parsed" Pydantic object without converting to dict

                    additional_kwargs=msg.additional_kwargs,

                ),

                generation_info=chat_result.generations[0].generation_info,

            )

            return

        if self.include_response_headers:

            raw_response = self.client.with_raw_response.create(**payload)

            response = raw_response.parse()

            base_generation_info = {"headers": dict(raw_response.headers)}

        else:

            response = self.client.create(**payload)

        with response:

            is_first_chunk = True

            for chunk in response:

                if not isinstance(chunk, dict):

                    chunk = chunk.model_dump()

                generation_chunk = self._convert_chunk_to_generation_chunk(

                    chunk,

                    default_chunk_class,

                    base_generation_info if is_first_chunk else {},

                )

                if generation_chunk is None:

                    continue

                default_chunk_class = generation_chunk.message.__class__

                logprobs = (generation_chunk.generation_info or {}).get("logprobs")

                if run_manager:

                    run_manager.on_llm_new_token(

                        generation_chunk.text, chunk=generation_chunk, logprobs=logprobs

                    )

                is_first_chunk = False

                yield generation_chunk



    async def _astream(

            self,

            messages: List[BaseMessage],

            stop: Optional[List[str]] = None,

            run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,

            **kwargs: Any,

    ) -> AsyncIterator[ChatGenerationChunk]:

        #self._rebuild_images_messages(messages, kwargs)

        kwargs["stream"] = True

        payload = self._get_request_payload(messages, stop=stop, extra_body=self.extra_body, **kwargs)

        default_chunk_class: Type[BaseMessageChunk] = AIMessageChunk

        base_generation_info = {}

        if "response_format" in payload and is_basemodel_subclass(

                payload["response_format"]

        ):

            # TODO: Add support for streaming with Pydantic response_format.

            # warnings.warn("Streaming with Pydantic response_format not yet supported.")

            chat_result = await self._agenerate(

                messages, stop, run_manager=run_manager, **kwargs

            )

            msg = chat_result.generations[0].message

            yield ChatGenerationChunk(

                message=AIMessageChunk(

                    **msg.dict(exclude={"type", "additional_kwargs"}),

                    # preserve the "parsed" Pydantic object without converting to dict

                    additional_kwargs=msg.additional_kwargs,

                ),

                generation_info=chat_result.generations[0].generation_info,

            )

            return

        if self.include_response_headers:

            raw_response = await self.async_client.with_raw_response.create(**payload)

            response = raw_response.parse()

            base_generation_info = {"headers": dict(raw_response.headers)}

        else:

            response = await self.async_client.create(**payload)

        async with response:

            is_first_chunk = True

            async for chunk in response:

                if not isinstance(chunk, dict):

                    chunk = chunk.model_dump()

                generation_chunk = self._convert_chunk_to_generation_chunk(

                    chunk,

                    default_chunk_class,

                    base_generation_info if is_first_chunk else {},

                )

                if generation_chunk is None:

                    continue

                default_chunk_class = generation_chunk.message.__class__

                logprobs = (generation_chunk.generation_info or {}).get("logprobs")

                if run_manager:

                    await run_manager.on_llm_new_token(

                        generation_chunk.text, chunk=generation_chunk, logprobs=logprobs

                    )

                is_first_chunk = False

                yield generation_chunk



    def _create_usage_metadata(self, oai_token_usage: dict) -> UsageMetadata:

        input_tokens = oai_token_usage.get("prompt_tokens", 0)

        output_tokens = oai_token_usage.get("completion_tokens", 0)

        total_tokens = oai_token_usage.get("total_tokens", input_tokens + output_tokens)

        input_token_details: dict = {

            "audio": (oai_token_usage.get("prompt_tokens_details") or {}).get(

                "audio_tokens"

            ),

            "cache_read": (oai_token_usage.get("prompt_tokens_details") or {}).get(

                "cached_tokens"

            ),

        }

        output_token_details: dict = {

            "audio": (oai_token_usage.get("completion_tokens_details") or {}).get(

                "audio_tokens"

            ),

            "reasoning": (oai_token_usage.get("completion_tokens_details") or {}).get(

                "reasoning_tokens"

            ),

        }

        return UsageMetadata(

            input_tokens=input_tokens,

            output_tokens=output_tokens,

            total_tokens=total_tokens,

            input_token_details=InputTokenDetails(

                **{k: v for k, v in input_token_details.items() if v is not None}

            ),

            output_token_details=OutputTokenDetails(

                **{k: v for k, v in output_token_details.items() if v is not None}

            ),

        )



    def _convert_chunk_to_generation_chunk(

            self,

            chunk: dict, default_chunk_class: Type, base_generation_info: Optional[Dict]

    ) -> Optional[ChatGenerationChunk]:

        token_usage = chunk.get("usage")

        choices = chunk.get("choices", [])



        usage_metadata: Optional[UsageMetadata] = (

            self._create_usage_metadata(token_usage) if token_usage else None

        )

        if len(choices) == 0:

            # logprobs is implicitly None

            generation_chunk = ChatGenerationChunk(

                message=default_chunk_class(content="", usage_metadata=usage_metadata)

            )

            return generation_chunk



        choice = choices[0]

        if choice["delta"] is None:

            return None



        message_chunk = self._convert_delta_to_message_chunk(

            choice["delta"], default_chunk_class

        )

        generation_info = {**base_generation_info} if base_generation_info else {}



        if finish_reason := choice.get("finish_reason"):

            generation_info["finish_reason"] = finish_reason

            if model_name := chunk.get("model"):

                generation_info["model_name"] = model_name

            if system_fingerprint := chunk.get("system_fingerprint"):

                generation_info["system_fingerprint"] = system_fingerprint



        logprobs = choice.get("logprobs")

        if logprobs:

            generation_info["logprobs"] = logprobs



        if usage_metadata and isinstance(message_chunk, AIMessageChunk):

            message_chunk.usage_metadata = usage_metadata



        generation_chunk = ChatGenerationChunk(

            message=message_chunk, generation_info=generation_info or None

        )

        return generation_chunk



    def _convert_delta_to_message_chunk(

            self,

            _dict: Mapping[str, Any],

            default_class: Type[BaseMessageChunk]

    ) -> BaseMessageChunk:

        id_ = _dict.get("id")

        role = cast(str, _dict.get("role"))

        content = cast(str, _dict.get("content") or "")

        reasoning_content = cast(str, _dict.get("reasoning_content") or "")

        additional_kwargs: Dict = {}

        if _dict.get("function_call"):

            function_call = dict(_dict["function_call"])

            if "name" in function_call and function_call["name"] is None:

                function_call["name"] = ""

            additional_kwargs["function_call"] = function_call

        tool_call_chunks = []

        if raw_tool_calls := _dict.get("tool_calls"):

            additional_kwargs["tool_calls"] = raw_tool_calls

            try:

                tool_call_chunks = [

                    tool_call_chunk(

                        name=rtc["function"].get("name"),

                        args=rtc["function"].get("arguments"),

                        id=rtc.get("id"),

                        index=rtc["index"],

                    )

                    for rtc in raw_tool_calls

                ]

            except KeyError:

                pass



        if role == "user" or default_class == HumanMessageChunk:

            return HumanMessageChunk(content=content, id=id_)

        elif role == "assistant" or default_class == AIMessageChunk:

            return AIMessageChunk(

                content=content,

                reasoning_content=reasoning_content,

                additional_kwargs=additional_kwargs,

                id=id_,

                tool_call_chunks=tool_call_chunks,  # type: ignore[arg-type]

            )

        elif role == "system" or default_class == SystemMessageChunk:

            return SystemMessageChunk(content=content, id=id_)

        elif role == "function" or default_class == FunctionMessageChunk:

            return FunctionMessageChunk(content=content, name=_dict["name"], id=id_)

        elif role == "tool" or default_class == ToolMessageChunk:

            return ToolMessageChunk(

                content=content, tool_call_id=_dict["tool_call_id"], id=id_

            )

        elif role or default_class == ChatMessageChunk:

            return ChatMessageChunk(content=content, role=role, id=id_)

        else:

            return default_class(content=content, reasoning_content=reasoning_content, id=id_)  # type: ignore



    def _generate_from_stream(self, stream: Iterator[ChatGenerationChunk]) -> ChatResult:

        """Generate from a stream.



            Args:

                stream: Iterator of ChatGenerationChunk.



            Returns:

                ChatResult: Chat result.

            """

        generation = next(stream, None)

        reasoning_content = ''

        if hasattr(generation.message, "reasoning_content"):

            reasoning_content = generation.message.reasoning_content

        if generation:

            for st in stream:

                reasoning_content += st.message.reasoning_content

                generation += st

        generation.message.__pydantic_extra__ = {'reasoning_content': reasoning_content}

        generation.__pydantic_extra__ = {'reasoning_content': reasoning_content}

        if generation is None:

            msg = "No generations found in stream."

            raise ValueError(msg)

        return ChatResult(

            generations=[

                ChatGeneration(

                    message=self._message_chunk_to_message(generation.message),

                    generation_info=generation.generation_info,

                )

            ]

        )



    async def _agenerate_from_stream(self, stream: AsyncIterator[ChatGenerationChunk]) -> ChatResult:

        """Async generate from a stream.



            Args:

                stream: Iterator of ChatGenerationChunk.



            Returns:

                ChatResult: Chat result.

            """

        chunks = [chunk async for chunk in stream]

        return await run_in_executor(None, self._generate_from_stream, iter(chunks))



    def _message_chunk_to_message(self, chunk: BaseMessageChunk) -> BaseMessage:

        """Convert a message chunk to a message.



        Args:

            chunk: Message chunk to convert.



        Returns:

            Message.

        """

        if not isinstance(chunk, BaseMessageChunk):

            return chunk

        # chunk classes always have the equivalent non-chunk class as their first parent

        ignore_keys = ["type"]

        if isinstance(chunk, AIMessageChunk):

            ignore_keys.append("tool_call_chunks")

        all_fields = {

            **chunk.__dict__,

            **(chunk.model_extra or {})

        }

        return chunk.__class__.__mro__[1](

            **{k: v for k, v in all_fields.items() if k not in ignore_keys}

        )

