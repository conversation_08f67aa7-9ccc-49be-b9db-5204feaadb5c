#!/usr/bin/env python
# -*- coding: utf-8 -*-
from config import get_config
from flask import redirect

'''
Buc SSO wiki
http://gitlab.alibaba-inc.com/buc/sso/wikis/home
'''

import urllib
import requests
import json


class AuthError(Exception):
    pass


class Buc<PERSON><PERSON><PERSON>andler():
    '''
        日常：https://login-test.alibaba-inc.com
        线上：https://login.alibaba-inc.com
    '''
    # SSO_HOST = "https://login.alibaba-inc.com"
    SSO_HOST = get_config().auth.buc_sso.host


    LOGIN_URL = SSO_HOST + '/ssoLogin.htm'
    COMMUNICATE_URL = SSO_HOST + '/rpc/sso/communicate.json'
    VALIDATE_SSO_TOKEN_URL = SSO_HOST + '/rpc/ssoToken/validateSsoToken.json'

    '''
        换成自己的APP_CODE和APP_NAME
    '''
    APP_CODE = get_config().auth.buc_sso.app_code #'40cbff03-d950-4b8e-8f38-e7b14f4c631d'
    APP_NAME = 'ecs-deep-diagnose'

    def authenticate_redirect_url(self, next):
        args = {
            'APP_NAME': self.APP_NAME,
            'BACK_URL': next,
        }
        return self.LOGIN_URL + '?' + urllib.parse.urlencode(args)

    def authenticate_redirect(self, next):
        return redirect(self.authenticate_redirect_url(next))

    def validate_sso_token(self, token):
        args = {
            'SSO_TOKEN': token,
            'APP_CODE': self.APP_CODE,
            'RETURN_USER': 'true',
        }

        response = requests.post(self.VALIDATE_SSO_TOKEN_URL, args)
        result = response.json()
        if not result[u'hasError']:
            user = json.loads(result['content'])
            user = self.normalize_user(user)
            # raise gen.Return(user)
            return user
        else:
            raise AuthError(result.get('content'))

    def get_authenticated_user(self, token):
        args = {
            'SSO_TOKEN': token,
            'APP_CODE': self.APP_CODE,
            'RETURN_USER': 'true',
        }


        response = requests.post(self.COMMUNICATE_URL, args)

        result = response.json()

        if not result[u'hasError']:
            user = json.loads(result['content'])
            user = self.normalize_user(user)
            return user
            # raise gen.Return(user)
        else:
            raise AuthError(result.get('content'))

    def normalize_user(self, user):
        result = {}
        for key in ('nickNameCn', 'tbWW'):
            if result.get('name'):
                break
            result['name'] = user.get(key)

        for key in ('emailAddr', 'account'):
            if result.get('email'):
                break
            result['email'] = user.get(key)

        result['realname'] = user.get('lastName')
        result['token'] = user.get('token')
        result['id'] = user.get('empId')
        result['department'] = user.get('depDesc')
        result["mobile"] = user.get("cellphone")
        result['nickNameCn'] = user.get('nickNameCn')
        result['emailAddr'] = f"https://work.alibaba-inc.com/photo/%s.80x80.jpg" % user.get('empId')
        return result


def main():
    ''' main function
    '''
    print('Done')


if __name__ == '__main__':
    main()
