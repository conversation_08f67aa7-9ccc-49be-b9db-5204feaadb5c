

"""
认证中间件模块 - 处理用户认证

本模块负责验证用户是否已登录，如果未登录则重定向到登录页面。
使用重构后的AuthService进行认证处理，提高代码的可维护性。
"""

from urllib import parse as url_parse

from fastapi.requests import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import RedirectResponse
from web.auth.auth_service import AuthService

from web.buc.buc_sso import BucAuthHandler


class AuthMiddleware(BaseHTTPMiddleware):
    """
    认证中间件，用于验证用户是否已登录
    
    使用AuthService进行统一的认证处理，简化认证流程。
    """
    
    def __init__(self, app):
        """
        初始化认证中间件
        
        Args:
            app: FastAPI应用实例
        """
        super().__init__(app)
        self.auth_service = AuthService()
    
    async def dispatch(self, request: Request, call_next):
        """
        处理请求
        
        验证用户是否已登录，如果未登录则重定向到登录页面。
        
        Args:
            request: 请求对象
            call_next: 下一个处理函数
            
        Returns:
            响应对象
        """
        # 使用AuthService验证用户身份
        authentication = await self.auth_service.authenticate(request)
        
        if authentication is False:
            # 用户未登录，重定向到登录页面
            back_url = url_parse.urljoin(request.url, "")
            return RedirectResponse(
                url=BucAuthHandler().authenticate_redirect_url(back_url)
            )
        
        # 用户已登录，继续处理请求
        response = await call_next(request)
        return response
