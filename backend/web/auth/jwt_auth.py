from datetime import datetime, timedelta
from typing import Optional

from fastapi import Request, HTTPException
from fastapi.security import HTTPAuthorizationCredentials
from fastapi.security import HTTPBearer
from jose import JWTError, jwt

from common.utils import logging_utils
from config import get_config
from web.models.model import UserModel

ALGORITHM = "HS256"


class JWTAuth:
    def __init__(self, secret_key: str = None, algorithm: str = ALGORITHM):
        if secret_key is None:
            config = get_config()
            secret_key = config.app.secret
        self.secret_key = secret_key
        self.algorithm = algorithm

    def create_token(self, data: int, expires_delta: Optional[timedelta] = None):
        """
        创建一个JWT令牌。

        参数:
        - data (int): 要编码的数据，通常包含用户信息等。
        - expires_delta (Optional[timedelta]): 令牌的过期时间增量。如果提供，将按照此增量计算过期时间；如果未提供，默认过期时间为15分钟。

        返回:
        - 返回经过编码的JWT令牌字符串。
        """
        # 复制data字典以避免修改原始数据
        to_encode = data.copy()
        # 判断是否提供了过期时间增量
        if expires_delta:
            # 根据提供的expires_delta计算过期时间
            expire = datetime.utcnow() + expires_delta
        else:
            # 使用默认的过期时间增量（15分钟）
            expire = datetime.utcnow() + timedelta(minutes=15)
        # 更新待编码数据，添加过期时间
        to_encode.update({"exp": expire})
        # 使用秘钥和算法对数据进行编码，返回编码后的令牌
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str):
        """
        验证JWT(token)的有效性。

        参数:
        - token: str，待验证的JWT令牌。

        返回值:
        - 如果验证成功，返回JWT中包含的用户名（str类型）。
        - 如果验证失败，返回None。
        """
        try:
            # 尝试使用JWT解码令牌，验证其合法性
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username: str = payload.get("sub")
            if username is None:
                # 如果解码后的payload中没有"sub"字段，抛出JWTError异常
                raise JWTError
            return username
        except JWTError:
            # 捕获JWTError异常，返回None表示验证失败
            return None

    async def authenticate(self, request: Request):
        """
        异步认证函数，用于验证请求的授权凭证是否有效。

        参数:
        - request: Request 类型，表示当前的请求对象。

        返回值:
        - 返回 True 表示认证成功，返回 False 表示认证失败。
        """
        try:
            # 尝试从请求中提取Bearer类型的授权凭证
            credentials: HTTPAuthorizationCredentials = await HTTPBearer().__call__(
                request
            )
            if not credentials:
                # 如果没有提取到授权凭证，抛出权限异常
                raise HTTPException(
                    status_code=403, detail="Invalid authorization code."
                )
            if not credentials.scheme == "Bearer":
                # 如果授权方案不是Bearer，抛出权限异常
                raise HTTPException(
                    status_code=403, detail="Invalid authentication scheme."
                )
            # 验证令牌的有效性
            payload = self.verify_token(credentials.credentials)
            if not payload:
                # 如果令牌无效，返回认证失败
                return False
            # 令牌有效，返回认证成功
            return True
        except Exception:
            # 任何异常情况下，均视为认证失败
            return False


    async def get_user(self, request: Request):
        """
        异步获取用户信息。

        参数:
        - request: Request 对象，包含客户端的请求信息。

        返回值:
        - UserModel 实例，包含用户访问权限和标识信息；如果认证失败或没有提供认证信息，则返回 None。
        """
        credentials = None
        try:
            # 尝试从请求中提取 HTTP Bearer 认证信息
            credentials: HTTPAuthorizationCredentials = await HTTPBearer().__call__(
                request
            )
        except Exception:
            # 认证过程出现异常，打印堆栈信息并返回 None
            import traceback

            traceback.print_exc()
            return None
        # 记录请求的认证信息
        logging_utils.app_detail(
            "auth",
            "headers",
            "headers",
            f"credentials: {request.headers}",
        )
        if credentials:
            # 记录已验证的认证信息
            logging_utils.app_detail(
                "auth",
                "get_current_user",
                "crednetials",
                f"credentials: {credentials}",
            )
            # 验证令牌并获取访问密钥
            access_key = self.verify_token(credentials.credentials)
            # 根据请求方法获取请求参数
            if request.method == "POST":
                request_json = await request.json()
            elif request.method in ("GET", "DELETE"):
                request_json = request.query_params
            else:
                request_json = await request.json()
            user_id = request_json.get("user_id")
            if user_id is None:
                user_id = ""
            # 记录用户 ID 信息
            logging_utils.app_detail(
                "auth",
                "get_current_user",
                "crednetials",
                f"credentials: {credentials} {user_id}",
            )
            # 构建并返回 JWT 用户模型
            return UserModel(
                access_key=access_key,
                user_id=user_id,
                user_name="",
                user_type="jwt",
            )
        else:
            # 认证信息不存在，返回 None
            return None

