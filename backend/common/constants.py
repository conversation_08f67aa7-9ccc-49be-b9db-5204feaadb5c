APP_HOME_DIR = '/home/<USER>/ecs-deep-diagnose'

# Chat workflow constants
DEFAULT_MAX_STEP_NUM = 20  # Maximum number of steps in a workflow
DEFAULT_AUTO_ACCEPTED_PLAN = True  # 是否自动接受计划
DEFAULT_ENABLE_BACKGROUND_INVESTIGATION = False  # 是否启用后台调查

# MCP服务器设置
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="
}

mcp_settings = {
    "servers": {
        "cloudbot": {  # Server ID
            "name": "cloudbot",
            "transport": "streamable_http",
            "env": None,
            "url": "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/",
            # Server URL
            "enabled_tools": [  # List of tools enabled for this server
                "getVmBasicInfo",
                "getNcBasicInfo",
                "runDiagnose",
                "listReportedOperationalEvents",
                "listMonitorExceptions",
                "listActionTrail",
                "ScreenShotDiagnose",
                "listOperationRecords",
                "listChangeRecords",
                "SubmitOps",
            ],
            "add_to_agents": ["researcher"],  # Which agents can use these tools
            "headers": headers
        },
        "vm_coredump": {  # Server ID
            "name": "vm_coredump",
            "transport": "streamable_http",
            "env": None,
            "url": "http://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/?token=************************************************",
            # Server URL
            "enabled_tools": [  # List of tools enabled for this server
                "get_vm_coredump"
            ],
            "add_to_agents": ["researcher"]  # Which agents can use these tools
        },
        "antv": {  # Server ID
            "name": "antv",
            "transport": "stdio",
            "env": None,
            "command": "npx",
            "args": [
                "-y",
                "@antv/mcp-server-chart"
            ],
            # Server URL
            "enabled_tools": [  # List of tools enabled for this server
                "generate_pie_chart"
            ],
            "add_to_agents": ["researcher"]  # Which agents can use these tools
        }
    }
}

