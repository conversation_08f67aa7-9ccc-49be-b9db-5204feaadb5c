# coding=utf-8

import base64

from common.security.keycenter import kc_key_processor
from common.security.keycenter import kc_key_sync


def init_service(server_host, publish_num):
    """Initialize KeyCenter service using server_host and publish_num.

    This function MUST be called before calling encrypt and descrypt
    :param server_host: KeyCenter server address
    :param publish_num: KeyCenter publish number
    """
    kc_key_sync.initialize(server_host, publish_num)


def encrypt(key_name, plain_text):
    """Encrypt plain text using given key, return cipher text

    :param key_name: name of the key, created on KeyCenter Console
    :param plain_text: text to encrypt
    :return cipher text
    """
    cipher_text = kc_key_processor.encrypt(key_name, plain_text)
    return base64.b64encode(cipher_text)


def decrypt(key_name, cipher_text):
    """Decrypt cipher text using given key, return plain text

    :param key_name: name of key, created on KeyCenter Console
    :param cipher_text: text to decrypt
    :return plain text
    """
    cipher_text = base64.b64decode(cipher_text)
    return kc_key_processor.decrypt(key_name, cipher_text)


if __name__ == "__main__":
    encrypted = encrypt("keycenter_demo_aes", "hello world")
    print(decrypt("keycenter_demo_aes", encrypted))
