# coding=utf-8

import base64

from common.security.keycenter import kc_key_processor
from common.security.keycenter import kc_key_sync


def init_service(server_host, publish_num):
    """Initialize KeyCenter service using server_host and publish_num.

    This function MUST be called before calling sign and verify
    :param server_host: KeyCenter server address
    :param publish_num: KeyCenter publish number
    """
    kc_key_sync.initialize(server_host, publish_num)


def sign(key_name, plain_text):
    """sign plain text using given key, return signed text

    :param key_name: name of the key, created on KeyCenter Console
    :param plain_text: text to sign
    :return signed text
    """
    sign_text = kc_key_processor.sign(key_name, plain_text)
    return base64.b64encode(sign_text.encode('utf-8'))


def verify(key_name, plain_text, sign_text):
    """verify signed text using given key, return result success or faild

    :param key_name: name of key, created on KeyCenter Console
    :param plain_text: text before sign
    :param sign_text: text after sign
    :return verify result(verify success or faild)
    """
    verify_text = base64.b64decode(sign_text)
    return kc_key_processor.verify(key_name, plain_text, sign_text)


if __name__ == "__main__":
    signed = sign("keycenter_demo_md5", "hello world")
    print(verify("keycenter_demo_md5", "hello world", signed))
